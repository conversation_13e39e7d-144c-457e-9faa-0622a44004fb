/**
 * Represents an exam question with multiple choice options in the new format
 */
export interface Question {
  question: string;
  options: {
    A: string;
    B: string;
    C: string;
    D: string;
    [key: string]: string; // Allow for additional options if needed
  };
  correctAnswers: string[]; // Array of correct answers (e.g., ["A"] or ["A", "C"])
  hint?: string; // Optional hint for the question
}

/**
 * Represents an exam question with multiple choice options in the old format
 * (for backward compatibility)
 */
export interface LegacyQuestion {
  題目: string;
  selectA: string;
  selectB: string;
  selectC: string;
  selectD: string;
  CorrectAnswer: string; // Can be a single letter or multiple letters (e.g., "A" or "AC")
}

/**
 * Represents an option in a randomized order
 */
export interface RandomizedOption {
  id: string; // Original option ID (A, B, C, D)
  text: string; // Option text content
  displayId: string; // Display ID for UI (A, B, C, D)
}

/**
 * Represents a user's answer to a question
 */
export interface UserAnswer {
  questionIndex: number;
  selectedAnswers: string[]; // Array of selected options (e.g., ["A", "C"])
  isCorrect: boolean;
  flagged?: boolean; // Indicates if the question is flagged for review
}

/**
 * Represents an exam session with results
 */
export interface ExamSession {
  date: string;
  score: number;
  totalQuestions: number;
  incorrectAnswers: UserAnswer[];
}
