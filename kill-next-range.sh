#!/bin/bash

# 設定欲檢查的 port 範圍
START_PORT=3000
END_PORT=3010

echo "🔍 掃描 port $START_PORT 到 $END_PORT 是否被佔用中..."

for ((port=$START_PORT; port<=$END_PORT; port++)); do
  PID=$(lsof -ti tcp:$port)

  if [ -n "$PID" ]; then
    PROC_NAME=$(ps -p $PID -o comm=)
    echo "⚠️  Port $port 被 PID $PID 的程式 [$PROC_NAME] 佔用，準備終止..."
    kill -9 $PID
    echo "🛑 已釋放 port $port"
  else
    echo "✅ Port $port 沒有被使用"
  fi
done

echo "🎉 所有佔用 port 的程序已清理完畢！"

