/**
 * Root layout component for the SCRUM exam application
 */
import type { Metada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "../styles/globals.css";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "通用考試系統",
  description: "隨機出題、多重選擇、自訂題數的智能考試平台",
};

/**
 * Root layout component that wraps all pages
 */
export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="zh-TW">
      <body className={inter.className}>
        <main className="min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
          {children}
        </main>
      </body>
    </html>
  );
}
