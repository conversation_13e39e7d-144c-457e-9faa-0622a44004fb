{"name": "scrum-exam", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"clsx": "^2.1.1", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "next": "^15.0.0", "react": "^18.2.0", "react-dom": "^18.2.0", "tailwind-merge": "^2.6.0"}, "devDependencies": {"@types/node": "^20.10.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "autoprefixer": "^10.4.21", "eslint": "^8.54.0", "eslint-config-next": "^15.0.0", "postcss": "^8.5.3", "tailwindcss": "^3.4.17", "tailwindcss-animate": "^1.0.7", "typescript": "^5.3.0"}}