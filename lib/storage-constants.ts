/**
 * 統一的 Storage 常量和接口定義
 */
import { Question, ExamSession, UserAnswer, RandomizedOption } from "../types/question";

// Storage Keys 常量
export const STORAGE_CONSTANTS = {
  EXAM_RESULTS: "exam_results",
  CURRENT_EXAM: "current_exam", 
  REVIEW_DATA: "review_data"
} as const;

// 當前考試狀態接口
export interface CurrentExamStorage {
  questions: Question[];
  randomizedOptions: {[questionIndex: number]: RandomizedOption[]};
  answerMappings: {[questionIndex: number]: {[displayId: string]: string}};
  originalAnswers: {[questionIndex: number]: string[]};
  mode: 'normal' | 'review';
  timestamp: string;
}

// 複習數據接口
export interface ReviewDataStorage {
  originalExamSession: ExamSession;
  incorrectQuestionIndices: number[];
  timestamp: string;
}

// 統一的 Storage 管理類
export class ExamStorageManager {
  private static isClient(): boolean {
    return typeof window !== 'undefined';
  }

  // 考試結果相關方法
  static saveExamResults(sessions: ExamSession[]): void {
    if (!this.isClient()) return;
    try {
      localStorage.setItem(STORAGE_CONSTANTS.EXAM_RESULTS, JSON.stringify(sessions));
    } catch (error) {
      console.error('Failed to save exam results:', error);
    }
  }

  static getExamResults(): ExamSession[] {
    if (!this.isClient()) return [];
    try {
      const data = localStorage.getItem(STORAGE_CONSTANTS.EXAM_RESULTS);
      return data ? JSON.parse(data) : [];
    } catch (error) {
      console.error('Failed to get exam results:', error);
      return [];
    }
  }

  // 當前考試相關方法
  static saveCurrentExam(examData: CurrentExamStorage): void {
    if (!this.isClient()) return;
    try {
      localStorage.setItem(STORAGE_CONSTANTS.CURRENT_EXAM, JSON.stringify(examData));
      console.log('當前考試數據已保存:', examData);
    } catch (error) {
      console.error('Failed to save current exam:', error);
    }
  }

  static getCurrentExam(): CurrentExamStorage | null {
    if (!this.isClient()) return null;
    try {
      const data = localStorage.getItem(STORAGE_CONSTANTS.CURRENT_EXAM);
      return data ? JSON.parse(data) : null;
    } catch (error) {
      console.error('Failed to get current exam:', error);
      return null;
    }
  }

  static clearCurrentExam(): void {
    if (!this.isClient()) return;
    try {
      localStorage.removeItem(STORAGE_CONSTANTS.CURRENT_EXAM);
      console.log('當前考試數據已清除');
    } catch (error) {
      console.error('Failed to clear current exam:', error);
    }
  }

  // 複習數據相關方法
  static saveReviewData(reviewData: ReviewDataStorage): void {
    if (!this.isClient()) return;
    try {
      localStorage.setItem(STORAGE_CONSTANTS.REVIEW_DATA, JSON.stringify(reviewData));
      console.log('複習數據已保存:', reviewData);
    } catch (error) {
      console.error('Failed to save review data:', error);
    }
  }

  static getReviewData(): ReviewDataStorage | null {
    if (!this.isClient()) return null;
    try {
      const data = localStorage.getItem(STORAGE_CONSTANTS.REVIEW_DATA);
      return data ? JSON.parse(data) : null;
    } catch (error) {
      console.error('Failed to get review data:', error);
      return null;
    }
  }

  static clearReviewData(): void {
    if (!this.isClient()) return;
    try {
      localStorage.removeItem(STORAGE_CONSTANTS.REVIEW_DATA);
      console.log('複習數據已清除');
    } catch (error) {
      console.error('Failed to clear review data:', error);
    }
  }

  // 清除所有數據
  static clearAllData(): void {
    if (!this.isClient()) return;
    try {
      Object.values(STORAGE_CONSTANTS).forEach(key => {
        localStorage.removeItem(key);
      });
      console.log('所有考試數據已清除');
    } catch (error) {
      console.error('Failed to clear all data:', error);
    }
  }

  // 遷移舊數據的輔助方法
  static migrateOldData(): void {
    if (!this.isClient()) return;
    
    console.log('開始遷移舊數據...');
    
    // 遷移考試結果
    const oldResults = localStorage.getItem('exam_results');
    if (oldResults) {
      this.saveExamResults(JSON.parse(oldResults));
    }

    // 清除舊的 storage keys
    const oldKeys = [
      'current_exam_questions',
      'randomized_options', 
      'answer_mapping',
      'original_answers',
      'review_questions',
      'original_exam_session'
    ];

    oldKeys.forEach(key => {
      if (localStorage.getItem(key)) {
        console.log(`清除舊數據: ${key}`);
        localStorage.removeItem(key);
      }
    });

    console.log('數據遷移完成');
  }
}
