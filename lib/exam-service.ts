/**
 * Service for managing exam questions and results
 */
import { Question, ExamSession, UserAnswer, RandomizedOption } from "../types/question";
import { shuffleArray } from "./utils";
import {
  ExamStorageManager,
  CurrentExamStorage,
  ReviewDataStorage,
  STORAGE_CONSTANTS
} from "./storage-constants";

/**
 * Loads questions from the JSON file
 * @returns Array of questions
 */
export async function loadQuestions(): Promise<Question[]> {
  try {
    console.log('開始載入題目...');

    // Use fetch API to load the JSON file with cache-busting parameter and no-cache headers
    const timestamp = new Date().getTime();
    const url = `/questions_new.json?t=${timestamp}`;
    console.log('請求 URL:', url);

    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      }
    });

    console.log('Response status:', response.status);
    console.log('Response ok:', response.ok);

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}, statusText: ${response.statusText}`);
    }

    // Get the text first to ensure proper parsing
    const text = await response.text();
    console.log('Response text length:', text.length);

    // Parse the JSON manually
    try {
      const questions = JSON.parse(text);
      console.log('成功解析 JSON，題目數量:', questions.length);

      // Verify that the hint property is properly loaded
      if (questions.length > 0) {
        console.log('First question:', questions[0]);
        console.log('Has hint property:', questions[0].hasOwnProperty('hint'));
        console.log('Hint value:', questions[0].hint);
      }

      return questions;
    } catch (parseError) {
      console.error('JSON parsing error:', parseError);
      console.error('Response text preview:', text.substring(0, 200));
      throw parseError;
    }
  } catch (error) {
    console.error("Failed to load questions:", error);
    if (error instanceof Error) {
      console.error('Error details:', {
        name: error.name,
        message: error.message,
        stack: error.stack
      });
    }
    return [];
  }
}

/**
 * Gets a batch of questions for an exam
 * @param count - Number of questions to include
 * @returns Array of questions
 */
export async function getQuestionBatch(count: number): Promise<Question[]> {
  try {
    // 清除之前的考試題目存儲
    clearCurrentExamQuestions();

    const allQuestions = await loadQuestions();

    // Validate count
    const validCount = Math.min(Math.max(1, count), allQuestions.length);

    let selectedQuestions: Question[];

    // Randomly select questions if there are more than requested
    if (allQuestions.length > validCount) {
      selectedQuestions = shuffleArray([...allQuestions]).slice(0, validCount);
    } else {
      selectedQuestions = [...allQuestions];
    }

    // 準備統一的考試數據結構
    const randomizedOptions: {[questionIndex: number]: RandomizedOption[]} = {};
    const answerMappings: {[questionIndex: number]: {[displayId: string]: string}} = {};
    const originalAnswers: {[questionIndex: number]: string[]} = {};

    // 處理每個題目，生成隨機化選項和映射關係
    selectedQuestions.forEach((question, index) => {
      console.log(`處理題目 ${index} 的數據:`, question.correctAnswers);

      // 保存原始答案
      originalAnswers[index] = question.correctAnswers;

      // 隨機化選項
      const result = randomizeOptions(question.options, index);

      // 保存隨機化選項和映射關係
      randomizedOptions[index] = result.randomizedOptions;
      answerMappings[index] = result.displayToOriginalMap;
    });

    // 使用統一的存儲結構保存當前考試數據
    const currentExamData: CurrentExamStorage = {
      questions: selectedQuestions,
      randomizedOptions,
      answerMappings,
      originalAnswers,
      mode: 'normal',
      timestamp: new Date().toISOString()
    };

    ExamStorageManager.saveCurrentExam(currentExamData);

    return selectedQuestions;
  } catch (error) {
    console.error("Failed to get question batch:", error);
    return [];
  }
}

/**
 * 將問題選項隨機化的結果類型
 */
export interface RandomizeOptionsResult {
  randomizedOptions: RandomizedOption[];
  displayToOriginalMap: Record<string, string>;
}

/**
 * 將問題選項隨機化
 * @param options - 原始選項對象
 * @param questionIndex - 問題索引，用於生成穩定的隨機化
 * @returns 隨機化後的選項數組和映射關係
 */
export function randomizeOptions(options: Record<string, string>, questionIndex: number): RandomizeOptionsResult {
  // 從客戶端存儲中查找是否已經有預先隨機化的選項
  const storedOptions = getRandomizedOptions(questionIndex);
  if (storedOptions && storedOptions.length > 0) {
    // 從存儲的選項中重建映射關係
    const displayToOriginalMap: Record<string, string> = {};
    for (const opt of storedOptions) {
      displayToOriginalMap[opt.displayId] = opt.id;
    }
    return {
      randomizedOptions: storedOptions,
      displayToOriginalMap
    };
  }

  // 原始選項 ID
  const originalIds = Object.keys(options);

  // 轉換為選項數組
  const optionsArray = originalIds.map(id => ({
    id, // 原始 ID
    text: options[id]
  }));

  // 使用一個穩定的隨機種子來確保相同問題的選項隨機化結果相同
  // 使用問題索引作為種子
  const shuffledOptions = shuffleArrayWithSeed(optionsArray, questionIndex);

  // 為隨機化後的選項分配新的顯示 ID 並建立映射關係
  const displayToOriginalMap: Record<string, string> = {};
  const randomizedOptions: RandomizedOption[] = shuffledOptions.map((option: {id: string, text: string}, index: number) => {
    const displayId = originalIds[index]; // 使用原始的 ID 相同的格式作為顯示 ID

    // 建立映射關係：顯示 ID -> 原始 ID
    displayToOriginalMap[displayId] = option.id;

    return {
      id: option.id, // 原始 ID
      displayId, // 顯示 ID
      text: option.text
    };
  });

  return {
    randomizedOptions,
    displayToOriginalMap
  };
}

/**
 * 使用特定種子進行隨機打亂數組
 * @param array - 要打亂的數組
 * @param seed - 種子數字
 * @returns 打亂後的數組
 */
function shuffleArrayWithSeed<T>(array: T[], seed: number): T[] {
  const newArray = [...array];
  let currentIndex = newArray.length;
  let temporaryValue: T;
  let randomIndex: number;

  // 在範圍 [0, 1) 內建立一個基於種子的自定義佈性隨機數
  const random = () => {
    const x = Math.sin(seed++) * 10000;
    return x - Math.floor(x);
  };

  // 當還有元素要打亂時
  while (currentIndex !== 0) {
    // 選擇一個剩餘的元素...
    randomIndex = Math.floor(random() * currentIndex);
    currentIndex -= 1;

    // 並與當前元素交換
    temporaryValue = newArray[currentIndex];
    newArray[currentIndex] = newArray[randomIndex];
    newArray[randomIndex] = temporaryValue;
  }

  return newArray;
}

/**
 * Checks if the user's answer is correct
 * @param correctAnswers - The array of correct answers (e.g., ["A"] or ["A", "C"])
 * @param selectedAnswers - The user's selected answers
 * @param randomizedOptions - (Optional) The randomized options to map display IDs to original IDs
 * @param questionIndex - (Optional) The index of the question to check
 * @param question - (Optional) The complete question object for text-based comparison
 * @returns Whether the answer is correct
 */
export function checkAnswer(
  correctAnswers: string[],
  selectedAnswers: string[],
  randomizedOptions?: RandomizedOption[],
  questionIndex?: number,
  question?: Question
): boolean {
  // If no answers selected, it's incorrect
  if (selectedAnswers.length === 0) return false;

  // 使用映射關係進行批改，這是最可靠的方法
  if (question && randomizedOptions) {
    // 創建顯示 ID 到原始 ID 的映射
    const displayToOriginalMap: {[key: string]: string} = {};
    randomizedOptions.forEach(option => {
      displayToOriginalMap[option.displayId] = option.id;
    });

    // 將用戶選擇的顯示 ID 轉換為原始 ID
    const normalizedSelectedAnswers = selectedAnswers.map(displayId =>
      displayToOriginalMap[displayId] || displayId
    );

    console.log('映射關係批改調試:', {
      selectedAnswers,
      correctAnswers,
      displayToOriginalMap,
      normalizedSelectedAnswers,
      randomizedOptions
    });

    // 檢查數組長度是否相同
    if (correctAnswers.length !== normalizedSelectedAnswers.length) {
      return false;
    }

    // 檢查所有選擇的答案是否都在正確答案中，並且所有正確答案都被選擇
    const result = normalizedSelectedAnswers.every(answer => correctAnswers.includes(answer)) &&
                   correctAnswers.every(answer => normalizedSelectedAnswers.includes(answer));

    console.log('映射關係批改結果:', result);
    return result;
  }

  // 如果提供了題目索引，嘗試從存儲中獲取答案映射關係
  if (questionIndex !== undefined) {
    const storedMapping = getAnswerMapping(questionIndex);
    if (storedMapping) {
      // 使用存儲的映射關係進行答案轉換
      const normalizedSelectedAnswers = selectedAnswers.map(displayId =>
        storedMapping[displayId] || displayId
      );

      // 檢查數組長度是否相同
      if (correctAnswers.length !== normalizedSelectedAnswers.length) return false;

      // 檢查所有選擇的答案是否都在正確答案中，並且所有正確答案都被選擇
      return normalizedSelectedAnswers.every(answer => correctAnswers.includes(answer)) &&
             correctAnswers.every(answer => normalizedSelectedAnswers.includes(answer));
    }
  }

  // 如果沒有存儲的映射關係或沒有提供題目索引，但提供了隨機選項
  if (randomizedOptions) {
    // 創建顯示 ID 到原始 ID 的完整映射
    const displayToOriginalMap: {[key: string]: string} = {};
    randomizedOptions.forEach(option => {
      displayToOriginalMap[option.displayId] = option.id;
    });

    // 將選擇的顯示 ID 轉換為原始 ID
    const normalizedSelectedAnswers = selectedAnswers.map(displayId =>
      displayToOriginalMap[displayId] || displayId
    );

    // 檢查數組長度是否相同
    if (correctAnswers.length !== normalizedSelectedAnswers.length) return false;

    // 檢查所有選擇的答案是否都在正確答案中，並且所有正確答案都被選擇
    return normalizedSelectedAnswers.every(answer => correctAnswers.includes(answer)) &&
           correctAnswers.every(answer => normalizedSelectedAnswers.includes(answer));
  }

  // 最後的備用方案：直接比較 ID
  // 檢查數組長度是否相同
  if (correctAnswers.length !== selectedAnswers.length) return false;

  // 檢查所有選擇的答案是否都在正確答案中，並且所有正確答案都被選擇
  return selectedAnswers.every(answer => correctAnswers.includes(answer)) &&
         correctAnswers.every(answer => selectedAnswers.includes(answer));
}

/**
 * Saves exam session results to local storage
 * @param session - The exam session to save
 */
export function saveExamSession(session: ExamSession): void {
  if (typeof window === "undefined") return;

  try {
    const existingSessions = getExamSessions();
    const updatedSessions = [session, ...existingSessions];

    ExamStorageManager.saveExamResults(updatedSessions);
  } catch (error) {
    console.error("Failed to save exam session:", error);
  }
}

/**
 * Gets all saved exam sessions from local storage
 * @returns Array of exam sessions
 */
export function getExamSessions(): ExamSession[] {
  return ExamStorageManager.getExamResults();
}

/**
 * Gets the most recent exam session
 * @returns The most recent exam session or null if none exists
 */
export function getLatestExamSession(): ExamSession | null {
  const sessions = getExamSessions();
  return sessions.length > 0 ? sessions[0] : null;
}

/**
 * Gets incorrect questions from the latest exam session
 * @returns Array of questions that were answered incorrectly
 */
export async function getIncorrectQuestions(): Promise<Question[]> {
  const latestSession = getLatestExamSession();
  if (!latestSession) return [];

  // 保存原始考試結果供列印使用
  saveOriginalExamSession(latestSession);

  const allQuestions = await loadQuestions();
  const incorrectQuestionIndices = latestSession.incorrectAnswers.map(
    (answer: UserAnswer) => answer.questionIndex
  );

  // 獲取指定索引的題目
  const incorrectQuestions = incorrectQuestionIndices.map((index: number) => allQuestions[index]);

  // 使用新的統一存儲架構保存複習數據
  const originalExamSession = getOriginalExamSession();
  saveReviewQuestions(incorrectQuestions, latestSession.incorrectAnswers, originalExamSession || undefined);

  return incorrectQuestions;
}

/**
 * 儲存當前考試題目到客戶端存儲 (已棄用，使用統一存儲)
 * @deprecated 使用 ExamStorageManager.saveCurrentExam 替代
 */
export function saveCurrentExamQuestions(questions: Question[]): void {
  console.warn('saveCurrentExamQuestions is deprecated, use ExamStorageManager.saveCurrentExam instead');
  // 為了向後兼容，暫時保留
}

/**
 * 從客戶端存儲獲取當前考試題目
 * @returns 存儲的題目數組，如果沒有則返回空數組
 */
export function getCurrentExamQuestions(): Question[] {
  const currentExam = ExamStorageManager.getCurrentExam();
  return currentExam ? currentExam.questions : [];
}

/**
 * 清除客戶端存儲的當前考試題目
 */
export function clearCurrentExamQuestions(): void {
  ExamStorageManager.clearCurrentExam();
  // 同時清除複習數據
  ExamStorageManager.clearReviewData();
  // 遷移舊數據
  ExamStorageManager.migrateOldData();
}

/**
 * 儲存題目的原始正確答案到客戶端 (已棄用，使用統一存儲)
 * @deprecated 使用統一的 CurrentExamStorage 結構
 */
export function saveOriginalAnswers(questionIndex: number, correctAnswers: string[]): void {
  console.warn('saveOriginalAnswers is deprecated, use unified CurrentExamStorage instead');
  // 為了向後兼容，暫時保留
}

/**
 * 獲取題目的原始正確答案
 * @param questionIndex - 題目在數組中的索引
 * @returns 正確答案數組，如果沒有則返回 null
 */
export function getOriginalAnswers(questionIndex: number): string[] | null {
  const currentExam = ExamStorageManager.getCurrentExam();
  if (!currentExam || !currentExam.originalAnswers) return null;

  return currentExam.originalAnswers[questionIndex] || null;
}

/**
 * 儲存答案映射關係到客戶端 (已棄用，使用統一存儲)
 * @deprecated 使用統一的 CurrentExamStorage 結構
 */
export function saveAnswerMapping(questionIndex: number, mapping: {[key: string]: string}): void {
  console.warn('saveAnswerMapping is deprecated, use unified CurrentExamStorage instead');
  // 為了向後兼容，暫時保留
}

/**
 * 獲取特定題目的答案映射關係
 * @param questionIndex - 題目在數組中的索引
 * @returns 顯示 ID 到原始 ID 的映射關係，如果沒有則返回 null
 */
export function getAnswerMapping(questionIndex: number): {[key: string]: string} | null {
  const currentExam = ExamStorageManager.getCurrentExam();
  if (!currentExam || !currentExam.answerMappings) return null;

  return currentExam.answerMappings[questionIndex] || null;
}

/**
 * 儲存每個題目的隨機排列選項
 * @param questionIndex - 題目在數組中的索引
 * @param options - 隨機排列後的選項
 */
export function saveRandomizedOptions(questionIndex: number, options: RandomizedOption[]): void {
  console.warn('saveRandomizedOptions is deprecated, use unified CurrentExamStorage instead');
  // 為了向後兼容，暫時保留
}

/**
 * 獲取特定題目的隨機排列選項
 * @param questionIndex - 題目在數組中的索引
 * @returns 隨機排列的選項數組，如果沒有則返回 null
 */
export function getRandomizedOptions(questionIndex: number): RandomizedOption[] | null {
  const currentExam = ExamStorageManager.getCurrentExam();
  if (!currentExam || !currentExam.randomizedOptions) return null;

  return currentExam.randomizedOptions[questionIndex] || null;
}

/**
 * 保存複習錯誤題目的專用數據 (使用新的統一存儲架構)
 * @param questions - 錯誤題目數組
 * @param incorrectAnswers - 用戶的錯誤答案
 * @param originalExamSession - 原始考試結果
 */
export function saveReviewQuestions(questions: Question[], incorrectAnswers: UserAnswer[], originalExamSession?: ExamSession): void {
  if (typeof window === 'undefined') return;

  try {
    // 獲取當前考試數據
    const currentExam = ExamStorageManager.getCurrentExam();
    if (!currentExam) {
      console.error('No current exam data found for review');
      return;
    }

    // 準備複習數據的映射關係
    const reviewRandomizedOptions: {[questionIndex: number]: RandomizedOption[]} = {};
    const reviewAnswerMappings: {[questionIndex: number]: {[displayId: string]: string}} = {};
    const reviewOriginalAnswers: {[questionIndex: number]: string[]} = {};

    // 為複習題目建立新的索引映射
    questions.forEach((question, reviewIndex) => {
      // 找到原始考試中對應的題目索引
      const originalIndex = incorrectAnswers[reviewIndex]?.questionIndex;
      if (originalIndex !== undefined) {
        // 保存原始考試的映射關係到複習索引
        if (currentExam.randomizedOptions[originalIndex]) {
          reviewRandomizedOptions[reviewIndex] = currentExam.randomizedOptions[originalIndex];
        }
        if (currentExam.answerMappings[originalIndex]) {
          reviewAnswerMappings[reviewIndex] = currentExam.answerMappings[originalIndex];
        }
        if (currentExam.originalAnswers[originalIndex]) {
          reviewOriginalAnswers[reviewIndex] = currentExam.originalAnswers[originalIndex];
        }
      }
    });

    // 創建複習模式的考試數據
    const reviewExamData: CurrentExamStorage = {
      questions,
      randomizedOptions: reviewRandomizedOptions,
      answerMappings: reviewAnswerMappings,
      originalAnswers: reviewOriginalAnswers,
      mode: 'review',
      timestamp: new Date().toISOString()
    };

    // 保存複習數據
    const reviewData: ReviewDataStorage = {
      originalExamSession: originalExamSession || {
        date: new Date().toISOString(),
        score: 0,
        totalQuestions: questions.length,
        incorrectAnswers
      },
      incorrectQuestionIndices: incorrectAnswers.map(answer => answer.questionIndex),
      timestamp: new Date().toISOString()
    };

    // 使用統一存儲管理器保存數據
    ExamStorageManager.saveCurrentExam(reviewExamData);
    ExamStorageManager.saveReviewData(reviewData);

    console.log('複習數據已保存 (新架構):', { reviewExamData, reviewData });
  } catch (error) {
    console.error('Failed to save review questions:', error);
  }
}

/**
 * 獲取複習錯誤題目的專用數據 (使用新的統一存儲架構)
 * @returns 複習數據或 null
 */
export function getReviewQuestions(): {
  questions: Question[],
  incorrectAnswers: UserAnswer[],
  originalRandomizedOptions?: {[key: number]: RandomizedOption[]},
  originalAnswerMappings?: {[key: number]: {[key: string]: string}},
  timestamp: string
} | null {
  // 為了向後兼容，返回舊格式的數據
  const currentExam = ExamStorageManager.getCurrentExam();
  const reviewData = ExamStorageManager.getReviewData();

  if (!currentExam || !reviewData || currentExam.mode !== 'review') {
    return null;
  }

  return {
    questions: currentExam.questions,
    incorrectAnswers: reviewData.originalExamSession.incorrectAnswers,
    originalRandomizedOptions: currentExam.randomizedOptions,
    originalAnswerMappings: currentExam.answerMappings,
    timestamp: reviewData.timestamp
  };
}

/**
 * 保存原始考試結果供列印使用 (使用新的統一存儲架構)
 * @param session - 原始考試結果
 */
export function saveOriginalExamSession(session: ExamSession): void {
  if (typeof window === 'undefined') return;

  try {
    // 使用 ReviewData 結構保存原始考試結果
    const reviewData: ReviewDataStorage = {
      originalExamSession: session,
      incorrectQuestionIndices: session.incorrectAnswers.map(answer => answer.questionIndex),
      timestamp: new Date().toISOString()
    };

    ExamStorageManager.saveReviewData(reviewData);
    console.log('原始考試結果已保存供列印使用 (新架構):', session);
  } catch (error) {
    console.error('Failed to save original exam session:', error);
  }
}

/**
 * 獲取原始考試結果供列印使用 (使用新的統一存儲架構)
 * @returns 原始考試結果或 null
 */
export function getOriginalExamSession(): ExamSession | null {
  try {
    const reviewData = ExamStorageManager.getReviewData();
    return reviewData ? reviewData.originalExamSession : null;
  } catch (error) {
    console.error('Failed to get original exam session:', error);
    return null;
  }
}

/**
 * 清除複習相關的存儲數據 (使用新的統一存儲架構)
 */
export function clearReviewData(): void {
  ExamStorageManager.clearReviewData();
}
