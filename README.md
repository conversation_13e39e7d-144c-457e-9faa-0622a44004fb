# SCRUM 考試系統

一個互動式的 SCRUM 考試網站，幫助用戶練習 SCRUM 相關知識並追蹤學習進度。

## 功能特色

- 一次顯示 5 題卡片式問題
- 紀錄錯誤並給予評分
- 提供暗示功能幫助解答
- 紀錄上次考試錯題結果，方便複習

## 技術架構

- Next.js 15+
- TypeScript
- Tailwind CSS
- 使用 JSON 檔案作為題庫來源

## 如何運行

1. 安裝依賴：
```bash
pnpm install
```

2. 啟動開發伺服器：
```bash
pnpm dev
```

3. 開啟瀏覽器訪問：
```
http://localhost:3000
```

## 專案結構

```
project-root/
├── app/                  # Next.js App Router
├── components/           # React 組件
│   ├── ui/               # UI 組件
│   ├── question-card.tsx # 問題卡片組件
│   ├── exam-page.tsx     # 考試頁面組件
│   └── exam-results.tsx  # 考試結果組件
├── lib/                  # 工具函數
│   ├── utils.ts          # 通用工具函數
│   └── exam-service.ts   # 考試相關服務
├── public/               # 靜態資源
│   └── scrum_questions.json # 題庫資料
├── styles/               # 全局樣式
│   └── globals.css       # Tailwind CSS 樣式
└── types/                # TypeScript 類型定義
    └── question.ts       # 問題相關類型定義
```

## 使用說明

1. 首頁顯示歡迎訊息和開始考試按鈕
2. 點擊開始考試後，系統會隨機選擇 5 題問題
3. 每題問題提供 A、B、C、D 四個選項
4. 選擇答案後，點擊下一題繼續
5. 完成所有問題後，系統會顯示得分和錯誤題目
6. 可以選擇重新考試或複習錯誤題目
