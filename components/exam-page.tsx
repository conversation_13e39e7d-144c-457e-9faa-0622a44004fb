/**
 * Main component for the general exam page
 */
"use client";

import React, { useState, useEffect, useRef, Suspense } from "react";
import dynamic from 'next/dynamic';
import { QuestionCard } from "./question-card";
import { But<PERSON> } from "./ui/button";
import { Question, UserAnswer, ExamSession, RandomizedOption } from "../types/question";
import { calculateScore } from "../lib/utils";
import {
  getQuestionBatch,
  saveExamSession,
  getIncorrectQuestions,
  checkAnswer,
  loadQuestions,
  randomizeOptions,
  getRandomizedOptions,
  clearCurrentExamQuestions,
  getCurrentExamQuestions,
  saveCurrentExamQuestions,
  getOriginalExamSession,
  getReviewQuestions,
  clearReviewData
} from "../lib/exam-service";

// Dynamically import ExamResults to avoid hydration errors
const ExamResults = dynamic(
  () => import('./exam-results').then(mod => ({ default: mod.ExamResults }))
);

/**
 * Main exam page component that manages the exam flow
 */
function ExamPage() {
  // State for questions and answers
  const [questions, setQuestions] = useState<Question[]>([]);
  const [allQuestions, setAllQuestions] = useState<Question[]>([]);
  const [userAnswers, setUserAnswers] = useState<UserAnswer[]>([]);

  // UI state
  const [currentIndex, setCurrentIndex] = useState(0);
  const [showResults, setShowResults] = useState(false);
  const [showAnswers, setShowAnswers] = useState(false); // 控制是否顯示答案
  const [isReviewMode, setIsReviewMode] = useState(false);
  const [showSetup, setShowSetup] = useState(true);
  const [isLoading, setIsLoading] = useState(true);
  const [isPrinting, setIsPrinting] = useState(false);

  // Configuration
  const [questionsPerExam, setQuestionsPerExam] = useState(10);

  // References
  const pdfRef = useRef<HTMLDivElement>(null);

  // Session state
  const [examSession, setExamSession] = useState<ExamSession | null>(null);
  const [isExamCompleted, setIsExamCompleted] = useState(false);
  const [showFlaggedQuestions, setShowFlaggedQuestions] = useState(false);

  /**
   * Loads all questions when the component mounts
   * Only runs on the client side
   */
  useEffect(() => {
    // Skip during server-side rendering
    if (typeof window === 'undefined') return;

    const fetchAllQuestions = async () => {
      setIsLoading(true);
      try {
        const questions = await loadQuestions();
        setAllQuestions(questions);
      } catch (error) {
        console.error("Failed to load questions:", error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchAllQuestions();
  }, []);

  /**
   * Loads a batch of questions for the exam
   */
  const loadExamQuestions = async (reviewIncorrect = false) => {
    setIsLoading(true);
    try {
      let loadedQuestions;

      if (reviewIncorrect) {
        loadedQuestions = await getIncorrectQuestions();
        setIsReviewMode(true);
      } else {
        loadedQuestions = await getQuestionBatch(questionsPerExam);
        setIsReviewMode(false);
      }

      setQuestions(loadedQuestions);
      const initialAnswers = Array(loadedQuestions.length).fill(null).map((_, i) => ({
        questionIndex: i,
        selectedAnswers: [], // Initialize with empty array for multiple selections
        isCorrect: false
      }));
      setUserAnswers(initialAnswers);

      // 在複習模式下，所有題目都是錯誤的，所以從第一題開始
      setCurrentIndex(0);

      setIsExamCompleted(false);
      setShowResults(false);
      setExamSession(null);
      setShowSetup(false);
    } catch (error) {
      console.error("Failed to load questions:", error);
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Checks if the user's answer is correct
   * @param questionIndex - The index of the question
   * @param selectedAnswers - The user's selected answers
   */
  const checkUserAnswer = (questionIndex: number, selectedAnswers: string[]): boolean => {
    const question = questions[questionIndex];

    // 獲取存儲在客戶端的隨機選項
    const randomizedOpts = getRandomizedOptions(questionIndex);

    if (!randomizedOpts) {
      // 如果沒有隨機選項，直接比較答案 ID
      return selectedAnswers.some(answer => question.correctAnswers.includes(answer));
    }

    // 使用隨機選項進行比較
    for (const selectedDisplayId of selectedAnswers) {
      // 找到用戶選擇的選項
      const selectedOption = randomizedOpts.find(opt => opt.displayId === selectedDisplayId);
      if (!selectedOption) continue;

      // 檢查這個選項的原始 ID 是否在正確答案中
      if (question.correctAnswers.includes(selectedOption.id)) {
        return true;
      }
    }

    return false;
  };

  /**
   * Handles user answer selection
   */
  const handleAnswer = (answers: string[]) => {
    const updatedAnswers = [...userAnswers];
    const question = questions[currentIndex];

    // 記錄用戶選擇的答案（僅在開發模式下）
    if (process.env.NODE_ENV === 'development') {
      console.log(`選擇的答案:`, answers);
      console.log(`問題正確答案:`, question.correctAnswers);
    }

    // 使用簡化的批改邏輯
    const isCorrect = checkUserAnswer(currentIndex, answers);

    if (process.env.NODE_ENV === 'development') {
      console.log(`批改結果 - 是否正確:`, isCorrect);
    }

    // Preserve the flagged status if it exists
    const flagged = updatedAnswers[currentIndex]?.flagged || false;

    updatedAnswers[currentIndex] = {
      questionIndex: currentIndex,
      selectedAnswers: answers,
      isCorrect: isCorrect,
      flagged: flagged
    };

    setUserAnswers(updatedAnswers);
  };

  /**
   * Moves to the next question
   * In review mode, skips to the next incorrect question
   */
  const handleNext = () => {
    if (isReviewMode) {
      // 在複習模式下，跳到下一個仍然錯誤的題目
      const nextIncorrectIndex = findNextIncorrectQuestion(currentIndex + 1);
      if (nextIncorrectIndex !== -1) {
        setCurrentIndex(nextIncorrectIndex);
      } else {
        // 沒有更多錯誤題目，完成複習
        completeExam();
      }
    } else {
      // 正常考試模式
      if (currentIndex < questions.length - 1) {
        setCurrentIndex(currentIndex + 1);
      } else {
        completeExam();
      }
    }
  };

  /**
   * Finds the next question that is still incorrect
   */
  const findNextIncorrectQuestion = (startIndex: number): number => {
    for (let i = startIndex; i < questions.length; i++) {
      const userAnswer = userAnswers[i];
      // 如果沒有回答或回答錯誤，返回這個索引
      if (!userAnswer || !userAnswer.isCorrect || userAnswer.selectedAnswers.length === 0) {
        return i;
      }
    }
    return -1; // 沒有找到錯誤題目
  };

  /**
   * Moves to the previous question
   * In review mode, skips to the previous incorrect question
   */
  const handlePrevious = () => {
    if (isReviewMode) {
      // 在複習模式下，跳到上一個仍然錯誤的題目
      const prevIncorrectIndex = findPreviousIncorrectQuestion(currentIndex - 1);
      if (prevIncorrectIndex !== -1) {
        setCurrentIndex(prevIncorrectIndex);
      }
    } else {
      // 正常考試模式
      if (currentIndex > 0) {
        setCurrentIndex(currentIndex - 1);
      }
    }
  };

  /**
   * Finds the previous question that is still incorrect
   */
  const findPreviousIncorrectQuestion = (startIndex: number): number => {
    for (let i = startIndex; i >= 0; i--) {
      const userAnswer = userAnswers[i];
      // 如果沒有回答或回答錯誤，返回這個索引
      if (!userAnswer || !userAnswer.isCorrect || userAnswer.selectedAnswers.length === 0) {
        return i;
      }
    }
    return -1; // 沒有找到錯誤題目
  };

  /**
   * Completes the exam and calculates results
   */
  const completeExam = () => {
    // 只設置考試完成狀態，但不顯示答案
    setIsExamCompleted(true);

    // 在複習模式下，使用原始考試的題目數量；否則使用當前題目數量
    const totalOriginalQuestions = isReviewMode ? questionsPerExam : questions.length;

    // 重新評估每個答案的正確性
    const updatedAnswers = [...userAnswers];
    let correctCount = 0;

    // 遍歷所有已回答的題目
    for (let i = 0; i < updatedAnswers.length; i++) {
      const answer = updatedAnswers[i];
      if (answer && answer.selectedAnswers.length > 0) {
        const question = questions[i];
        let isCorrect = false;

        // 使用統一的檢查方法
        isCorrect = checkUserAnswer(i, answer.selectedAnswers);

        // 更新答案正確性
        updatedAnswers[i] = {
          ...answer,
          isCorrect: isCorrect
        };

        if (isCorrect) {
          correctCount++;
        }
      }
    }

    // 更新用戶答案的正確性
    setUserAnswers(updatedAnswers);

    // 只統計已回答的題目
    const answeredQuestions = updatedAnswers.filter(answer => answer !== null && answer.selectedAnswers.length > 0);
    const incorrectAnswers = updatedAnswers.filter(answer => answer && !answer.isCorrect);

    // 使用原始題目數量計算分數
    const score = calculateScore(correctCount, totalOriginalQuestions);

    console.log(`考試完成: 正確 ${correctCount}/${totalOriginalQuestions}, 分數 ${score}%`);
    console.log(`錯誤題目: ${incorrectAnswers.length} 題`);

    const session: ExamSession = {
      date: new Date().toISOString(),
      score,
      totalQuestions: totalOriginalQuestions, // 使用原始題目數量
      incorrectAnswers
    };

    setExamSession(session);

    // Only save session if not in review mode
    if (!isReviewMode) {
      saveExamSession(session);
    }
  };

  /**
   * Shows the exam results and answers
   */
  const handleShowResults = () => {
    setShowResults(true);
    setShowAnswers(true); // 設置顯示答案狀態
  };

  /**
   * Resets the exam to its initial state
   */
  const handleRestart = () => {
    // 清除客戶端存儲的題目和選項數據
    clearCurrentExamQuestions();

    // 清除複習相關數據
    clearReviewData();

    // 重置所有考試狀態
    setQuestions([]);
    setUserAnswers([]);
    setCurrentIndex(0);
    setShowResults(false);
    setShowAnswers(false); // 重置顯示答案狀態
    setIsReviewMode(false);
    setIsExamCompleted(false);
    setExamSession(null);
    setIsLoading(false);
    setIsPrinting(false);
    setShowSetup(true);
    setShowFlaggedQuestions(false);
  };

  /**
   * Reviews incorrect questions from the last exam
   */
  const handleReviewIncorrect = () => {
    loadExamQuestions(true);
  };



  /**
   * Toggles the flagged status of the current question
   */
  const handleToggleFlag = () => {
    const updatedAnswers = [...userAnswers];

    // Ensure there's an answer object for the current question
    if (!updatedAnswers[currentIndex]) {
      updatedAnswers[currentIndex] = {
        questionIndex: currentIndex,
        selectedAnswers: [],
        isCorrect: false,
        flagged: true
      };
    } else {
      // Toggle the flagged status
      updatedAnswers[currentIndex] = {
        ...updatedAnswers[currentIndex],
        flagged: !updatedAnswers[currentIndex].flagged
      };
    }

    setUserAnswers(updatedAnswers);
  };

  /**
   * Shows all flagged questions
   */
  const handleShowFlaggedQuestions = () => {
    setShowFlaggedQuestions(true);
  };

  /**
   * Returns to the normal exam view
   */
  const handleReturnToExam = () => {
    setShowFlaggedQuestions(false);
  };

  /**
   * Jumps to a specific flagged question
   */
  const handleJumpToQuestion = (index: number) => {
    setCurrentIndex(index);
    setShowFlaggedQuestions(false);
  };

  /**
   * Handles the number of questions change
   */
  const handleQuestionsChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseInt(e.target.value);
    if (!isNaN(value) && value > 0 && value <= allQuestions.length) {
      setQuestionsPerExam(value);
    }
  };

  /**
   * Starts the exam with the selected number of questions
   */
  const handleStartExam = () => {
    loadExamQuestions();
  };

  /**
   * Prints the exam questions and answers using the browser's print functionality
   * This function is only called on the client side
   */
  const handlePrintExam = () => {
    // Create a printable version in a new window
    const printWindow = window.open('', '_blank');
    if (!printWindow) {
      alert('Please allow pop-ups to print the exam');
      return;
    }

    // Get current date on client side
    const currentDate = new Date().toLocaleDateString();

    // 獲取原始考試結果（如果在複習模式下）
    const originalSession = getOriginalExamSession();
    const printSession = originalSession || examSession;

    if (!printSession) {
      alert('無法獲取考試結果數據');
      return;
    }

    // 獲取要列印的題目和答案數據
    let printQuestions = questions;
    let printAnswers = userAnswers;

    // 如果在複習模式且有原始考試數據，使用原始數據
    if (isReviewMode && originalSession) {
      const reviewData = getReviewQuestions();
      if (reviewData) {
        printQuestions = reviewData.questions;
        printAnswers = reviewData.incorrectAnswers;
      }
    }

    // Generate the HTML content for printing
    const printContent = `
      <!DOCTYPE html>
      <html>
      <head>
        <title>考試結果報告</title>
        <style>
          body { font-family: Arial, sans-serif; margin: 20px; }
          h1 { color: #2980b9; }
          .date { color: #7f8c8d; margin-bottom: 20px; }
          .summary { background: #f8f9fa; padding: 15px; border-radius: 5px; margin-bottom: 20px; }
          .score { font-size: 24px; font-weight: bold; color: #2980b9; }
          table { width: 100%; border-collapse: collapse; margin-top: 20px; }
          th { background-color: #2980b9; color: white; padding: 10px; text-align: left; }
          td { padding: 10px; border-bottom: 1px solid #ddd; vertical-align: top; }
          .correct { color: green; font-weight: bold; }
          .incorrect { color: red; font-weight: bold; }
          .question-text { max-width: 300px; word-wrap: break-word; }
          @media print {
            button { display: none; }
          }
        </style>
      </head>
      <body>
        <h1>考試結果報告</h1>
        <div class="date">列印日期: ${currentDate}</div>
        <div class="date">考試日期: ${new Date(printSession.date).toLocaleDateString()}</div>
        <button onclick="window.print();" style="padding: 10px 20px; background: #2980b9; color: white; border: none; cursor: pointer; margin-bottom: 20px;">列印</button>

        <div class="summary">
          <div class="score">總分: ${printSession.score}%</div>
          <div>總題數: ${printSession.totalQuestions} 題</div>
          <div>錯誤題數: ${printSession.incorrectAnswers.length} 題</div>
          <div>正確題數: ${printSession.totalQuestions - printSession.incorrectAnswers.length} 題</div>
        </div>

        <table>
          <thead>
            <tr>
              <th style="width: 5%">#</th>
              <th style="width: 40%">題目</th>
              <th style="width: 15%">您的答案</th>
              <th style="width: 15%">正確答案</th>
              <th style="width: 10%">結果</th>
            </tr>
          </thead>
          <tbody>
            ${printQuestions.map((question, index) => {
              const userAnswer = printAnswers[index]?.selectedAnswers.join(', ') || '未作答';
              const correctAnswer = question.correctAnswers.join(', ');
              const isCorrect = printAnswers[index]?.isCorrect;
              return `
                <tr>
                  <td>${index + 1}</td>
                  <td class="question-text">${question.question}</td>
                  <td>${userAnswer}</td>
                  <td>${correctAnswer}</td>
                  <td class="${isCorrect ? 'correct' : 'incorrect'}">${isCorrect ? '正確' : '錯誤'}</td>
                </tr>
              `;
            }).join('')}
          </tbody>
        </table>
      </body>
      </html>
    `;

    // Write the content to the new window and trigger print
    printWindow.document.open();
    printWindow.document.write(printContent);
    printWindow.document.close();
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <p className="text-lg">載入考題中...</p>
      </div>
    );
  }

  if (showResults && examSession) {
    return (
      <div className="w-full max-w-3xl mx-auto">
        <ExamResults
          session={examSession}
          onRestart={handleRestart}
          onReviewIncorrect={handleReviewIncorrect}
        />

        <div className="mt-6">
          <Button
            onClick={handlePrintExam}
            className="w-full bg-white border border-gray-300 text-gray-700 hover:bg-gray-50"
          >
            列印考試結果
          </Button>
        </div>
      </div>
    );
  }

  if (showSetup) {
    return (
      <div className="w-full max-w-3xl mx-auto">
        <div className="bg-white rounded-lg shadow-lg p-8 mb-8">
          <h2 className="text-2xl font-semibold mb-6 text-blue-600">考試設定</h2>

          <div className="mb-8">
            <div className="flex items-center justify-between mb-2">
              <label className="block text-gray-700 font-medium" htmlFor="questionsCount">
                考試題數
              </label>
              <span className="text-sm text-gray-500">可選範圍: 1-{allQuestions.length}</span>
            </div>
            <div className="relative">
              <input
                id="questionsCount"
                type="number"
                min="1"
                max={allQuestions.length}
                value={questionsPerExam}
                onChange={handleQuestionsChange}
                className="shadow-sm border border-gray-300 rounded-md w-full py-3 px-4 text-gray-700 leading-tight focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all"
              />
              <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                <span className="text-gray-400">題</span>
              </div>
            </div>
            <p className="mt-2 text-sm text-gray-500">選擇你想要的考試題目數量</p>
          </div>

          <div className="space-y-4">
            <Button
              onClick={handleStartExam}
              className="w-full bg-blue-600 hover:bg-blue-700 text-white py-3 transition-colors"
              disabled={allQuestions.length === 0}
              size="lg"
            >
              開始考試
            </Button>

            <div className="text-center text-sm text-gray-500 mt-4">
              隨機出題、多重選擇、答案打亂的智能考試系統
            </div>
          </div>
        </div>
      </div>
    );
  }

  /**
   * 返回主頁面功能
   */
  const handleReturnToHome = () => {
    // 重置所有狀態並返回主頁面
    handleRestart();
  };

  /**
   * 重新做錯題測試，重置當前錯誤題目的答案狀態
   * 用戶可以重新回答錯誤題目
   */
  const handleRetestIncorrect = () => {
    setIsLoading(true);

    try {
      // 獲取當前存儲的錯誤題目（不需要重新載入）
      const currentQuestions = questions;

      if (currentQuestions.length === 0) {
        console.error("No questions available for retest");
        return;
      }

      // 重置考試狀態，但保持相同的題目
      setCurrentIndex(0);
      setUserAnswers(Array(currentQuestions.length).fill(null).map((_, i) => ({
        questionIndex: i,
        selectedAnswers: [], // 初始化為空數組
        isCorrect: false
      })));
      setIsExamCompleted(false);
      setIsReviewMode(true);
      setShowResults(false);
      setShowAnswers(false); // 重要：隱藏答案，讓用戶可以重新選擇
      setExamSession(null);

      console.log(`重新開始錯題測試: ${currentQuestions.length} 題`);
    } catch (error) {
      console.error("Failed to restart incorrect questions test:", error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="w-full max-w-3xl mx-auto" ref={pdfRef}>
      <div className="mb-6 flex justify-between items-center">
        <h2 className="text-xl font-bold text-gray-900">
          {isReviewMode ? "複習錯誤題目" : "通用考試系統"}
        </h2>
        <div className="flex items-center space-x-2">
          {isReviewMode && (
            <Button
              onClick={handleReturnToHome}
              className="text-sm py-1 px-3 bg-white border border-gray-300 text-gray-700 hover:bg-gray-50"
            >
              返回主頁
            </Button>
          )}
          <div className="text-sm font-medium bg-blue-600 text-white px-3 py-1 rounded-full">
            {questions.length > 0 ? (
              isReviewMode ? (
                `問題 ${currentIndex + 1} / ${questions.length} (${userAnswers.filter(a => a && a.isCorrect).length} 已正確)`
              ) : (
                `問題 ${currentIndex + 1} / ${questions.length}`
              )
            ) : ""}
          </div>
        </div>
      </div>

      {questions.length > 0 && (
        <QuestionCard
          question={questions[currentIndex]}
          questionIndex={currentIndex}
          onAnswer={handleAnswer}
          userAnswers={userAnswers[currentIndex]?.selectedAnswers || []}
          showResult={showAnswers}
        />
      )}

      {isReviewMode && (
        <div className="mt-4 mb-2">
          <Button
            onClick={handleRetestIncorrect}
            className="w-full bg-white border border-blue-300 text-blue-700 hover:bg-blue-50"
          >
            重新做錯題測試
          </Button>
        </div>
      )}

      <div className="flex justify-between mt-6 w-full">
        <div className="flex space-x-2">
          <Button
            onClick={handlePrevious}
            disabled={isReviewMode ? findPreviousIncorrectQuestion(currentIndex - 1) === -1 : currentIndex === 0}
            className="min-w-24 px-4 bg-gray-200 hover:bg-gray-300 text-gray-800 border border-gray-300 font-medium disabled:opacity-50 disabled:cursor-not-allowed"
          >
            ← {isReviewMode ? "上個錯題" : "上一題"}
          </Button>

          <Button
            onClick={handleToggleFlag}
            className={`px-3 border ${userAnswers[currentIndex]?.flagged ? 'bg-amber-100 text-amber-800 border-amber-300' : 'bg-white text-gray-600 border-gray-300 hover:bg-gray-50'}`}
            title="標記為待檢查/等會做"
          >
            {userAnswers[currentIndex]?.flagged ? '取消標記' : '標記待檢查'} ⚠️
          </Button>
        </div>

        <div className="flex space-x-2">
          <Button
            onClick={handleShowFlaggedQuestions}
            className="px-3 bg-white text-amber-700 border border-amber-300 hover:bg-amber-50 disabled:opacity-50 disabled:cursor-not-allowed"
            disabled={!userAnswers.some(answer => answer?.flagged)}
          >
            查看標記問題
          </Button>

          {isExamCompleted ? (
            <Button
              onClick={handleShowResults}
              className="min-w-24 px-4 bg-blue-600 text-white hover:bg-blue-700"
            >
              查看結果
            </Button>
          ) : (
            <Button
              onClick={handleNext}
              disabled={userAnswers[currentIndex]?.selectedAnswers.length === 0}
              className="min-w-24 px-4 bg-blue-600 text-white hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isReviewMode ? (
                findNextIncorrectQuestion(currentIndex + 1) === -1 ? "完成複習" : "下個錯題"
              ) : (
                currentIndex < questions.length - 1 ? "下一題" : "完成考試"
              )}
            </Button>
          )}
        </div>
      </div>

      {showFlaggedQuestions ? (
        <div className="bg-white rounded-lg shadow-md p-6 mt-6">
          <h3 className="text-xl font-bold mb-4 text-gray-900">待檢查/等會做的問題</h3>
          {userAnswers.some(answer => answer?.flagged) ? (
            <ul className="space-y-3 mb-6">
              {userAnswers.map((answer, index) => {
                if (!answer?.flagged) return null;
                return (
                  <li key={index} className="border border-amber-300 rounded-lg p-3 bg-amber-50">
                    <div className="flex justify-between items-center">
                      <div className="flex items-center">
                        <span className="inline-flex items-center justify-center bg-amber-500 text-white w-6 h-6 rounded-full mr-2 text-sm font-bold">{index + 1}</span>
                        <span className="text-gray-900 font-medium truncate max-w-md">
                          {questions[index]?.question.length > 60
                            ? questions[index]?.question.substring(0, 60) + "..."
                            : questions[index]?.question}
                        </span>
                      </div>
                      <Button
                        onClick={() => handleJumpToQuestion(index)}
                        className="text-sm py-1 px-3 bg-white text-amber-700 border border-amber-300 hover:bg-amber-100"
                      >
                        前往檢查
                      </Button>
                    </div>
                    <div className="ml-8 mt-1 text-sm text-gray-600">
                      {answer.selectedAnswers.length > 0
                        ? `已選擇: ${answer.selectedAnswers.join(", ")}`
                        : "尚未作答"}
                    </div>
                  </li>
                );
              })}
            </ul>
          ) : (
            <p className="text-gray-700 mb-6">目前沒有標記的問題</p>
          )}
          <Button
            onClick={handleReturnToExam}
            className="w-full bg-blue-600 text-white hover:bg-blue-700"
          >
            返回考試
          </Button>
        </div>
      ) : null}
    </div>
  );
}

export { ExamPage };
export default ExamPage;
