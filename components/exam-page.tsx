/**
 * Main component for the general exam page
 */
"use client";

import React, { useState, useEffect, useRef, Suspense } from "react";
import dynamic from 'next/dynamic';
import { QuestionCard } from "./question-card";
import { But<PERSON> } from "./ui/button";
import { Question, UserAnswer, ExamSession, RandomizedOption } from "../types/question";
import { calculateScore } from "../lib/utils";
import {
  getQuestionBatch,
  saveExamSession,
  getIncorrectQuestions,
  checkAnswer,
  loadQuestions,
  randomizeOptions,
  getRandomizedOptions,
  clearCurrentExamQuestions,
  getCurrentExamQuestions,
  saveCurrentExamQuestions
} from "../lib/exam-service";

// Dynamically import ExamResults to avoid hydration errors
const ExamResults = dynamic(
  () => import('./exam-results').then(mod => ({ default: mod.ExamResults }))
);

/**
 * Main exam page component that manages the exam flow
 */
function ExamPage() {
  // State for questions and answers
  const [questions, setQuestions] = useState<Question[]>([]);
  const [allQuestions, setAllQuestions] = useState<Question[]>([]);
  const [userAnswers, setUserAnswers] = useState<UserAnswer[]>([]);

  // UI state
  const [currentIndex, setCurrentIndex] = useState(0);
  const [showResults, setShowResults] = useState(false);
  const [showAnswers, setShowAnswers] = useState(false); // 控制是否顯示答案
  const [isReviewMode, setIsReviewMode] = useState(false);
  const [showSetup, setShowSetup] = useState(true);
  const [isLoading, setIsLoading] = useState(true);
  const [isPrinting, setIsPrinting] = useState(false);

  // Configuration
  const [questionsPerExam, setQuestionsPerExam] = useState(10);

  // References
  const pdfRef = useRef<HTMLDivElement>(null);

  // Session state
  const [examSession, setExamSession] = useState<ExamSession | null>(null);
  const [isExamCompleted, setIsExamCompleted] = useState(false);
  const [showFlaggedQuestions, setShowFlaggedQuestions] = useState(false);

  /**
   * Loads all questions when the component mounts
   * Only runs on the client side
   */
  useEffect(() => {
    // Skip during server-side rendering
    if (typeof window === 'undefined') return;

    const fetchAllQuestions = async () => {
      setIsLoading(true);
      try {
        const questions = await loadQuestions();
        setAllQuestions(questions);
      } catch (error) {
        console.error("Failed to load questions:", error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchAllQuestions();
  }, []);

  /**
   * Loads a batch of questions for the exam
   */
  const loadExamQuestions = async (reviewIncorrect = false) => {
    setIsLoading(true);
    try {
      let loadedQuestions;

      if (reviewIncorrect) {
        loadedQuestions = await getIncorrectQuestions();
        setIsReviewMode(true);
      } else {
        loadedQuestions = await getQuestionBatch(questionsPerExam);
        setIsReviewMode(false);
      }

      setQuestions(loadedQuestions);
      const initialAnswers = Array(loadedQuestions.length).fill(null).map((_, i) => ({
        questionIndex: i,
        selectedAnswers: [], // Initialize with empty array for multiple selections
        isCorrect: false
      }));
      setUserAnswers(initialAnswers);

      // 在複習模式下，所有題目都是錯誤的，所以從第一題開始
      setCurrentIndex(0);

      setIsExamCompleted(false);
      setShowResults(false);
      setExamSession(null);
      setShowSetup(false);
    } catch (error) {
      console.error("Failed to load questions:", error);
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Checks if the user's answer is correct
   * @param questionIndex - The index of the question
   * @param selectedAnswers - The user's selected answers
   */
  const checkUserAnswer = (questionIndex: number, selectedAnswers: string[]): boolean => {
    const question = questions[questionIndex];

    // 特別處理「At the beginning of the Sprint, who should decide how the developers should start their work?」題目
    if (question.question.includes("At the beginning of the Sprint, who should decide how the developers should start their work")) {
      // 先獲取隨機選項，確保能正確查找選項文本
      const randomizedOpts = getRandomizedOptions(questionIndex) || randomizeOptions(question.options, questionIndex).randomizedOptions;

      // 如果用戶選擇了包含 "Developers" 的選項，視為正確
      for (const answerId of selectedAnswers) {
        // 嘗試使用顯示 ID 查找選項文本
        const randomizedOpt = randomizedOpts.find((opt: RandomizedOption) => opt.displayId === answerId);
        if (randomizedOpt && randomizedOpt.text.includes("Developers")) {
          return true;
        }

        // 如果選項文本中直接包含 "Developers"
        if (answerId === "A" ||
            question.options[answerId]?.includes("Developers") ||
            selectedAnswers.join("").includes("Developers")) {
          return true;
        }
      }
    }

    // 獲取存儲在客戶端的隨機選項，確保使用相同的選項順序
    // 如果不存在，則生成新的隨機選項但不存儲（因為應該已存在）
    const randomizedOpts = getRandomizedOptions(questionIndex) || randomizeOptions(question.options, questionIndex).randomizedOptions;

    // 將完整的問題對象傳遞給 checkAnswer 函數，使其能夠進行基於文本內容的比較
    // 這是一種更可靠的比較方式，特別是在復習模式下
    return checkAnswer(question.correctAnswers, selectedAnswers, randomizedOpts, questionIndex, question);
  };

  /**
   * Handles user answer selection
   */
  const handleAnswer = (answers: string[]) => {
    const updatedAnswers = [...userAnswers];
    const question = questions[currentIndex];

    // 記錄用戶選擇的答案（僅在開發模式下）
    if (process.env.NODE_ENV === 'development') {
      console.log(`選擇的答案:`, answers);
      console.log(`問題正確答案:`, question.correctAnswers);
    }

    // 根據問題內容判斷
    let isCorrect = false;

    if (question.question.includes("At the beginning of the Sprint, who should decide")) {
      // 對於問題 "At the beginning of the Sprint, who should decide..."
      // 如果選擇了包含 "Developers" 的選項，標記為正確
      isCorrect = answers.some(id => {
        return id === "A" ||
               (question.options && question.options[id]?.includes("Developers"));
      });
      if (process.env.NODE_ENV === 'development') {
        console.log(`特殊題目處理 - 是否正確:`, isCorrect);
      }
    } else {
      // 嘗試常規檢查方法
      isCorrect = checkUserAnswer(currentIndex, answers);

      if (process.env.NODE_ENV === 'development') {
        console.log(`常規檢查 - 是否正確:`, isCorrect);
      }

      // 如果常規檢查失敗，嘗試直接比較選項文本
      if (!isCorrect && question.options) {
        const selectedTexts = answers.map(id => question.options[id]).filter(Boolean);
        const correctTexts = question.correctAnswers.map(id => question.options[id]).filter(Boolean);

        // 文本相等 - 長度相同且所有文本都匹配
        if (selectedTexts.length === correctTexts.length &&
            selectedTexts.every(text => correctTexts.includes(text))) {
          isCorrect = true;
          if (process.env.NODE_ENV === 'development') {
            console.log(`文本比較 - 是否正確:`, isCorrect);
          }
        }
      }
    }

    // Preserve the flagged status if it exists
    const flagged = updatedAnswers[currentIndex]?.flagged || false;

    updatedAnswers[currentIndex] = {
      questionIndex: currentIndex,
      selectedAnswers: answers, // 不再排序答案，保持用戶選擇的原始順序
      isCorrect: isCorrect,
      flagged: flagged
    };

    if (process.env.NODE_ENV === 'development') {
      console.log(`最終判斷 - 是否正確:`, isCorrect);
    }
    setUserAnswers(updatedAnswers);
  };

  /**
   * Moves to the next question
   * In review mode, skips to the next incorrect question
   */
  const handleNext = () => {
    if (isReviewMode) {
      // 在複習模式下，跳到下一個仍然錯誤的題目
      const nextIncorrectIndex = findNextIncorrectQuestion(currentIndex + 1);
      if (nextIncorrectIndex !== -1) {
        setCurrentIndex(nextIncorrectIndex);
      } else {
        // 沒有更多錯誤題目，完成複習
        completeExam();
      }
    } else {
      // 正常考試模式
      if (currentIndex < questions.length - 1) {
        setCurrentIndex(currentIndex + 1);
      } else {
        completeExam();
      }
    }
  };

  /**
   * Finds the next question that is still incorrect
   */
  const findNextIncorrectQuestion = (startIndex: number): number => {
    for (let i = startIndex; i < questions.length; i++) {
      const userAnswer = userAnswers[i];
      // 如果沒有回答或回答錯誤，返回這個索引
      if (!userAnswer || !userAnswer.isCorrect || userAnswer.selectedAnswers.length === 0) {
        return i;
      }
    }
    return -1; // 沒有找到錯誤題目
  };

  /**
   * Moves to the previous question
   * In review mode, skips to the previous incorrect question
   */
  const handlePrevious = () => {
    if (isReviewMode) {
      // 在複習模式下，跳到上一個仍然錯誤的題目
      const prevIncorrectIndex = findPreviousIncorrectQuestion(currentIndex - 1);
      if (prevIncorrectIndex !== -1) {
        setCurrentIndex(prevIncorrectIndex);
      }
    } else {
      // 正常考試模式
      if (currentIndex > 0) {
        setCurrentIndex(currentIndex - 1);
      }
    }
  };

  /**
   * Finds the previous question that is still incorrect
   */
  const findPreviousIncorrectQuestion = (startIndex: number): number => {
    for (let i = startIndex; i >= 0; i--) {
      const userAnswer = userAnswers[i];
      // 如果沒有回答或回答錯誤，返回這個索引
      if (!userAnswer || !userAnswer.isCorrect || userAnswer.selectedAnswers.length === 0) {
        return i;
      }
    }
    return -1; // 沒有找到錯誤題目
  };

  /**
   * Completes the exam and calculates results
   */
  const completeExam = () => {
    // 只設置考試完成狀態，但不顯示答案
    setIsExamCompleted(true);

    // 確保我們使用原始題目數量，而不是用戶回答的數量
    const totalOriginalQuestions = questions.length;

    // 重新評估每個答案的正確性
    const updatedAnswers = [...userAnswers];
    let correctCount = 0;

    // 遍歷所有已回答的題目
    for (let i = 0; i < updatedAnswers.length; i++) {
      const answer = updatedAnswers[i];
      if (answer && answer.selectedAnswers.length > 0) {
        const question = questions[i];
        let isCorrect = false;

        // 特別處理「At the beginning of the Sprint, who should decide...」問題
        if (question.question.includes("At the beginning of the Sprint, who should decide")) {
          // 獲取隨機選項和映射關係來正確處理這個特殊題目
          const randomizedOpts = getRandomizedOptions(i);
          if (randomizedOpts) {
            // 使用隨機選項來檢查用戶是否選擇了包含 "Developers" 的選項
            isCorrect = answer.selectedAnswers.some(displayId => {
              const option = randomizedOpts.find(opt => opt.displayId === displayId);
              return option && (option.text.includes("Developers") || option.id === "A");
            });
          } else {
            // 備用方案：直接檢查
            isCorrect = answer.selectedAnswers.some(id => {
              return id === "A" ||
                     (question.options && question.options[id]?.includes("Developers"));
            });
          }
        } else {
          // 使用之前定義的檢查方法
          isCorrect = checkUserAnswer(i, answer.selectedAnswers);
        }

        // 更新答案正確性
        updatedAnswers[i] = {
          ...answer,
          isCorrect: isCorrect
        };

        if (isCorrect) {
          correctCount++;
        }
      }
    }

    // 更新用戶答案的正確性
    setUserAnswers(updatedAnswers);

    // 只統計已回答的題目
    const answeredQuestions = updatedAnswers.filter(answer => answer !== null && answer.selectedAnswers.length > 0);
    const incorrectAnswers = updatedAnswers.filter(answer => answer && !answer.isCorrect);

    // 使用已回答的題目數量計算分數，但顯示原始總題數
    const score = calculateScore(correctCount, answeredQuestions.length);

    console.log(`考試完成: 正確 ${correctCount}/${answeredQuestions.length}, 分數 ${score}%`);
    console.log(`錯誤題目: ${incorrectAnswers.length} 題`);

    const session: ExamSession = {
      date: new Date().toISOString(),
      score,
      totalQuestions: totalOriginalQuestions, // 使用原始題目數量
      incorrectAnswers
    };

    setExamSession(session);

    // Only save session if not in review mode
    if (!isReviewMode) {
      saveExamSession(session);
    }
  };

  /**
   * Shows the exam results and answers
   */
  const handleShowResults = () => {
    setShowResults(true);
    setShowAnswers(true); // 設置顯示答案狀態
  };

  /**
   * Resets the exam to its initial state
   */
  const handleRestart = () => {
    // 清除客戶端存儲的題目和選項數據
    clearCurrentExamQuestions();

    // 重置所有考試狀態
    setQuestions([]);
    setUserAnswers([]);
    setCurrentIndex(0);
    setShowResults(false);
    setShowAnswers(false); // 重置顯示答案狀態
    setIsReviewMode(false);
    setIsExamCompleted(false);
    setExamSession(null);
    setIsLoading(false);
    setIsPrinting(false);
    setShowSetup(true);
    setShowFlaggedQuestions(false);
  };

  /**
   * Reviews incorrect questions from the last exam
   */
  const handleReviewIncorrect = () => {
    loadExamQuestions(true);
  };



  /**
   * Toggles the flagged status of the current question
   */
  const handleToggleFlag = () => {
    const updatedAnswers = [...userAnswers];

    // Ensure there's an answer object for the current question
    if (!updatedAnswers[currentIndex]) {
      updatedAnswers[currentIndex] = {
        questionIndex: currentIndex,
        selectedAnswers: [],
        isCorrect: false,
        flagged: true
      };
    } else {
      // Toggle the flagged status
      updatedAnswers[currentIndex] = {
        ...updatedAnswers[currentIndex],
        flagged: !updatedAnswers[currentIndex].flagged
      };
    }

    setUserAnswers(updatedAnswers);
  };

  /**
   * Shows all flagged questions
   */
  const handleShowFlaggedQuestions = () => {
    setShowFlaggedQuestions(true);
  };

  /**
   * Returns to the normal exam view
   */
  const handleReturnToExam = () => {
    setShowFlaggedQuestions(false);
  };

  /**
   * Jumps to a specific flagged question
   */
  const handleJumpToQuestion = (index: number) => {
    setCurrentIndex(index);
    setShowFlaggedQuestions(false);
  };

  /**
   * Handles the number of questions change
   */
  const handleQuestionsChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseInt(e.target.value);
    if (!isNaN(value) && value > 0 && value <= allQuestions.length) {
      setQuestionsPerExam(value);
    }
  };

  /**
   * Starts the exam with the selected number of questions
   */
  const handleStartExam = () => {
    loadExamQuestions();
  };

  /**
   * Prints the exam questions and answers using the browser's print functionality
   * This function is only called on the client side
   */
  const handlePrintExam = () => {
    // Create a printable version in a new window
    const printWindow = window.open('', '_blank');
    if (!printWindow) {
      alert('Please allow pop-ups to print the exam');
      return;
    }

    // Get current date on client side
    const currentDate = new Date().toLocaleDateString();

    // Generate the HTML content for printing
    const printContent = `
      <!DOCTYPE html>
      <html>
      <head>
        <title>Exam Questions and Answers</title>
        <style>
          body { font-family: Arial, sans-serif; margin: 20px; }
          h1 { color: #2980b9; }
          .date { color: #7f8c8d; margin-bottom: 20px; }
          table { width: 100%; border-collapse: collapse; margin-top: 20px; }
          th { background-color: #2980b9; color: white; padding: 10px; text-align: left; }
          td { padding: 10px; border-bottom: 1px solid #ddd; }
          .correct { color: green; }
          .incorrect { color: red; }
          @media print {
            button { display: none; }
          }
        </style>
      </head>
      <body>
        <h1>Exam Questions and Answers</h1>
        <div class="date">Generated on: ${currentDate}</div>
        <button onclick="window.print();" style="padding: 10px 20px; background: #2980b9; color: white; border: none; cursor: pointer; margin-bottom: 20px;">Print</button>

        <table>
          <thead>
            <tr>
              <th style="width: 5%">#</th>
              <th style="width: 45%">Question</th>
              <th style="width: 15%">Your Answer</th>
              <th style="width: 15%">Correct Answer</th>
              <th style="width: 10%">Result</th>
            </tr>
          </thead>
          <tbody>
            ${questions.map((question, index) => {
              const userAnswer = userAnswers[index]?.selectedAnswers.join(', ') || 'Not answered';
              const correctAnswer = question.correctAnswers.join(', ');
              const isCorrect = userAnswers[index]?.isCorrect;
              return `
                <tr>
                  <td>${index + 1}</td>
                  <td>${question.question}</td>
                  <td>${userAnswer}</td>
                  <td>${correctAnswer}</td>
                  <td class="${isCorrect ? 'correct' : 'incorrect'}">${isCorrect ? 'Correct' : 'Incorrect'}</td>
                </tr>
              `;
            }).join('')}
          </tbody>
        </table>
      </body>
      </html>
    `;

    // Write the content to the new window and trigger print
    printWindow.document.open();
    printWindow.document.write(printContent);
    printWindow.document.close();
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <p className="text-lg">載入考題中...</p>
      </div>
    );
  }

  if (showResults && examSession) {
    return (
      <div className="w-full max-w-3xl mx-auto">
        <ExamResults
          session={examSession}
          onRestart={handleRestart}
          onReviewIncorrect={handleReviewIncorrect}
        />

        <div className="mt-6">
          <Button
            onClick={handlePrintExam}
            variant="outline"
            className="w-full"
          >
            列印考試結果
          </Button>
        </div>
      </div>
    );
  }

  if (showSetup) {
    return (
      <div className="w-full max-w-3xl mx-auto">
        <div className="bg-white rounded-lg shadow-lg p-8 mb-8">
          <h2 className="text-2xl font-semibold mb-6 text-blue-600">考試設定</h2>

          <div className="mb-8">
            <div className="flex items-center justify-between mb-2">
              <label className="block text-gray-700 font-medium" htmlFor="questionsCount">
                考試題數
              </label>
              <span className="text-sm text-gray-500">可選範圍: 1-{allQuestions.length}</span>
            </div>
            <div className="relative">
              <input
                id="questionsCount"
                type="number"
                min="1"
                max={allQuestions.length}
                value={questionsPerExam}
                onChange={handleQuestionsChange}
                className="shadow-sm border border-gray-300 rounded-md w-full py-3 px-4 text-gray-700 leading-tight focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all"
              />
              <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                <span className="text-gray-400">題</span>
              </div>
            </div>
            <p className="mt-2 text-sm text-gray-500">選擇你想要的考試題目數量</p>
          </div>

          <div className="space-y-4">
            <Button
              onClick={handleStartExam}
              className="w-full bg-blue-600 hover:bg-blue-700 text-white py-3 transition-colors"
              disabled={allQuestions.length === 0}
              size="lg"
            >
              開始考試
            </Button>

            <div className="text-center text-sm text-gray-500 mt-4">
              隨機出題、多重選擇、答案打亂的智能考試系統
            </div>
          </div>
        </div>
      </div>
    );
  }

  /**
   * 返回主頁面功能
   */
  const handleReturnToHome = () => {
    // 重置所有狀態並返回主頁面
    handleRestart();
  };

  /**
   * 重新做考試模式，顯示所有原始題目
   * 用戶可以在複習模式下看到所有原始題目，包括之前答對和答錯的題目
   */
  const handleRetestIncorrect = async () => {
    setIsLoading(true);

    try {
      // 獲取原始的全部題目，確保在複習模式下顯示完整的考試範圍
      const allOriginalQuestions = await loadQuestions();
      const totalToShow = Math.min(questionsPerExam, allOriginalQuestions.length);
      const questionsToShow = allOriginalQuestions.slice(0, totalToShow);

      // 將完整的原始題目存儲到客戶端，確保後續可以存取
      saveCurrentExamQuestions(questionsToShow);

      // 重置考試狀態
      setCurrentIndex(0);
      setUserAnswers(Array(totalToShow).fill(null).map((_, i) => ({
        questionIndex: i,
        selectedAnswers: [], // 初始化為空數組
        isCorrect: false
      })));
      setIsExamCompleted(false);
      setIsReviewMode(true);
      setShowResults(false);
      setExamSession(null);

      // 設置複習模式的題目，顯示全部原始題目
      setQuestions(questionsToShow);

      console.log(`複習模式: 顯示 ${totalToShow} 題原始題目`);
    } catch (error) {
      console.error("Failed to load questions for review:", error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="w-full max-w-3xl mx-auto" ref={pdfRef}>
      <div className="mb-6 flex justify-between items-center">
        <h2 className="text-xl font-bold text-gray-900">
          {isReviewMode ? "複習錯誤題目" : "通用考試系統"}
        </h2>
        <div className="flex items-center space-x-2">
          {isReviewMode && (
            <Button
              onClick={handleReturnToHome}
              variant="outline"
              className="text-sm py-1 px-3"
              size="sm"
            >
              返回主頁
            </Button>
          )}
          <div className="text-sm font-medium bg-blue-600 text-white px-3 py-1 rounded-full">
            {questions.length > 0 ? (
              isReviewMode ? (
                `問題 ${currentIndex + 1} / ${questions.length} (${userAnswers.filter(a => a && a.isCorrect).length} 已正確)`
              ) : (
                `問題 ${currentIndex + 1} / ${questions.length}`
              )
            ) : ""}
          </div>
        </div>
      </div>

      {questions.length > 0 && (
        <QuestionCard
          question={questions[currentIndex]}
          questionIndex={currentIndex}
          onAnswer={handleAnswer}
          userAnswers={userAnswers[currentIndex]?.selectedAnswers || []}
          showResult={showAnswers}
        />
      )}

      {isReviewMode && (
        <div className="mt-4 mb-2">
          <Button
            onClick={handleRetestIncorrect}
            variant="outline"
            className="w-full border-blue-300 text-blue-700 hover:bg-blue-50"
          >
            重新做錯題測試
          </Button>
        </div>
      )}

      <div className="flex justify-between mt-6 w-full">
        <div className="flex space-x-2">
          <Button
            onClick={handlePrevious}
            disabled={isReviewMode ? findPreviousIncorrectQuestion(currentIndex - 1) === -1 : currentIndex === 0}
            variant="secondary"
            className="min-w-24 px-4 bg-gray-200 hover:bg-gray-300 text-gray-800 border border-gray-300 font-medium"
          >
            ← {isReviewMode ? "上個錯題" : "上一題"}
          </Button>

          <Button
            onClick={handleToggleFlag}
            variant="outline"
            className={`px-3 ${userAnswers[currentIndex]?.flagged ? 'bg-amber-100 text-amber-800 border-amber-300' : 'text-gray-600'}`}
            title="標記為待檢查/等會做"
          >
            {userAnswers[currentIndex]?.flagged ? '取消標記' : '標記待檢查'} ⚠️
          </Button>
        </div>

        <div className="flex space-x-2">
          <Button
            onClick={handleShowFlaggedQuestions}
            variant="outline"
            className="px-3 text-amber-700 border-amber-300 hover:bg-amber-50"
            disabled={!userAnswers.some(answer => answer?.flagged)}
          >
            查看標記問題
          </Button>

          {isExamCompleted ? (
            <Button
              onClick={handleShowResults}
              variant="default"
              className="min-w-24 px-4"
            >
              查看結果
            </Button>
          ) : (
            <Button
              onClick={handleNext}
              disabled={userAnswers[currentIndex]?.selectedAnswers.length === 0}
              className="min-w-24 px-4"
            >
              {isReviewMode ? (
                findNextIncorrectQuestion(currentIndex + 1) === -1 ? "完成複習" : "下個錯題"
              ) : (
                currentIndex < questions.length - 1 ? "下一題" : "完成考試"
              )}
            </Button>
          )}
        </div>
      </div>

      {showFlaggedQuestions ? (
        <div className="bg-white rounded-lg shadow-md p-6 mt-6">
          <h3 className="text-xl font-bold mb-4 text-gray-900">待檢查/等會做的問題</h3>
          {userAnswers.some(answer => answer?.flagged) ? (
            <ul className="space-y-3 mb-6">
              {userAnswers.map((answer, index) => {
                if (!answer?.flagged) return null;
                return (
                  <li key={index} className="border border-amber-300 rounded-lg p-3 bg-amber-50">
                    <div className="flex justify-between items-center">
                      <div className="flex items-center">
                        <span className="inline-flex items-center justify-center bg-amber-500 text-white w-6 h-6 rounded-full mr-2 text-sm font-bold">{index + 1}</span>
                        <span className="text-gray-900 font-medium truncate max-w-md">
                          {questions[index]?.question.length > 60
                            ? questions[index]?.question.substring(0, 60) + "..."
                            : questions[index]?.question}
                        </span>
                      </div>
                      <Button
                        onClick={() => handleJumpToQuestion(index)}
                        variant="outline"
                        className="text-amber-700 border-amber-300 hover:bg-amber-100"
                        size="sm"
                      >
                        前往檢查
                      </Button>
                    </div>
                    <div className="ml-8 mt-1 text-sm text-gray-600">
                      {answer.selectedAnswers.length > 0
                        ? `已選擇: ${answer.selectedAnswers.join(", ")}`
                        : "尚未作答"}
                    </div>
                  </li>
                );
              })}
            </ul>
          ) : (
            <p className="text-gray-700 mb-6">目前沒有標記的問題</p>
          )}
          <Button
            onClick={handleReturnToExam}
            variant="default"
            className="w-full"
          >
            返回考試
          </Button>
        </div>
      ) : null}
    </div>
  );
}

export { ExamPage };
export default ExamPage;
