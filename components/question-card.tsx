/**
 * Component for displaying an exam question as a card with randomized options
 */
"use client";

import React, { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, CardFooter, CardHeader, CardTitle } from "./ui/card";
import { Button } from "./ui/button";
import { Question, RandomizedOption } from "../types/question";
import { randomizeOptions, checkAnswer } from "../lib/exam-service";

interface QuestionCardProps {
  question: Question;
  questionIndex: number;
  onAnswer: (answers: string[]) => void;
  userAnswers: string[];
  showResult: boolean;
}

/**
 * Component that displays a single question card with multiple choice options
 */
export function QuestionCard({
  question,
  questionIndex,
  onAnswer,
  userAnswers,
  showResult,
}: QuestionCardProps) {
  const [showHint, setShowHint] = useState(false);
  const [randomizedOptions, setRandomizedOptions] = useState<RandomizedOption[]>([]);
  const correctAnswers = question.correctAnswers;

  // Get the number of correct answers
  const correctAnswersCount = question.correctAnswers.length;

  // Randomize options only on client-side
  useEffect(() => {
    // Only run on the client side
    if (typeof window === 'undefined') return;

    // 使用題目索引獲取或生成隨機選項
    // 這樣確保在複習模式下選項的順序保持一致
    const randomized = randomizeOptions(question.options, questionIndex);
    setRandomizedOptions(randomized.randomizedOptions);
  }, [question, questionIndex]);

  /**
   * Toggles the selection of an answer option
   * @param optionId - 選項的原始 ID
   */
  const toggleOption = (optionId: string) => {
    // 找到選項對應的顯示 ID
    const option = randomizedOptions.find(opt => opt.id === optionId);
    if (!option) return;

    const displayId = option.displayId;

    // 使用顯示 ID 來處理選項選擇，這樣用戶的選擇會不受選項順序的影響
    if (userAnswers.includes(displayId)) {
      // 如果已選擇，則移除
      onAnswer(userAnswers.filter(id => id !== displayId));
    } else {
      // 如果未選擇，則添加
      onAnswer([...userAnswers, displayId]);
    }
  };

  /**
   * Gets custom styles based on the user's answer and whether the result is shown
   * @param optionId - 選項的原始 ID
   */
  const getButtonClassName = (optionId: string): string => {
    // 找到選項對應的顯示 ID
    const option = randomizedOptions.find(opt => opt.id === optionId);
    if (!option) return "hover:bg-slate-50";

    const displayId = option.displayId;

    if (!showResult) {
      // 在考試中 - 使用顯示 ID 來確定選項樣式
      return userAnswers.includes(displayId)
        ? "border-2 border-blue-500 bg-blue-50 text-blue-800"
        : "hover:bg-slate-50";
    }

    // 顯示結果時 - 使用原始 ID 來確定正確答案
    if (correctAnswers.includes(option.id)) {
      // 正確答案
      return "bg-emerald-100 text-emerald-800 border border-emerald-300 hover:bg-emerald-200";
    }

    if (userAnswers.includes(displayId)) {
      // 錯誤選擇的答案
      return "bg-rose-100 text-rose-800 border border-rose-300 hover:bg-rose-200";
    }

    // 未選擇的錯誤答案
    return "text-slate-500 hover:bg-slate-50";
  };

  /**
   * Gets button variant based on the user's answer and whether the result is shown
   * @param optionId - 選項的原始 ID
   */
  const getButtonVariant = (optionId: string): "outline" | "ghost" => {
    // 找到選項對應的顯示 ID
    const option = randomizedOptions.find(opt => opt.id === optionId);
    if (!option) return "ghost";

    const displayId = option.displayId;

    // 使用顯示 ID 確定按鈕樣式，與 toggleOption 保持一致
    return (userAnswers.includes(displayId) || showResult) ? "outline" : "ghost";
  };

  /**
   * Generates a hint based on the question's hint field or correct answers
   */
  const getHint = () => {
    // 如果問題中有提供 hint，優先使用
    if (question.hint) {
      return question.hint;
    }

    // 否則根據正確答案數量生成通用提示
    if (correctAnswers.length > 1) {
      return `此問題需要選擇 ${correctAnswers.length} 個正確答案。仔細閱讀每個選項，並考慮其在 Scrum 中的應用。`;
    }

    return "仔細考慮所有選項並選擇最符合 Scrum 原則的答案。";
  };

  return (
    <Card className="w-full mb-6">
      <CardHeader>
        <CardTitle className="text-xl text-slate-800 break-words">
          <div className="flex items-start">
            <span className="inline-flex items-center justify-center bg-blue-600 text-white w-7 h-7 rounded-full mr-3 flex-shrink-0 text-base font-medium">{questionIndex + 1}</span>
            <span className="flex-grow">{question.question}</span>
          </div>
        </CardTitle>
        <div className="text-sm mt-2">
          {correctAnswersCount > 1
            ? <span className="bg-amber-100 text-amber-800 px-3 py-1 rounded-full font-medium text-xs">可複選</span>
            : <span className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full font-medium text-xs">單選</span>}
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {randomizedOptions.map((option) => (
          <Button
            key={option.id}
            variant={getButtonVariant(option.id)}
            className={`justify-start text-left py-6 mb-2 w-full ${getButtonClassName(option.id)} whitespace-normal break-words`}
            onClick={() => toggleOption(option.id)}
            disabled={showResult}
          >
            <div className="flex items-start w-full">
              <span className="font-bold mr-3 flex-shrink-0 min-w-6">{option.displayId}.</span>
              <span className="inline-block flex-grow">{option.text}</span>
              {userAnswers.includes(option.displayId) && (
                <span className="ml-2 flex-shrink-0 text-blue-600">✓</span>
              )}
            </div>
          </Button>
        ))}

        {showHint && (
          <div className="bg-amber-50 border-l-4 border-amber-400 p-4 mb-4 rounded-r-md">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-amber-500" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2h-1V9z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <p className="text-sm text-amber-800 font-medium">{getHint()}</p>
              </div>
            </div>
          </div>
        )}
      </CardContent>
      <CardFooter className="flex justify-between items-center">
        <Button
          variant="secondary"
          onClick={() => setShowHint(!showHint)}
          className="min-w-24 px-4"
        >
          {showHint ? "隱藏提示" : "顯示提示"}
        </Button>

        {showResult && (
          <div className="text-right">
            <span className={
              // 特別處理「At the beginning of the Sprint, who should decide...」問題
              question.question.includes("At the beginning of the Sprint, who should decide") &&
              userAnswers.some(id => {
                // 從隨機選項中找出對應選項
                const opt = randomizedOptions.find(o => o.displayId === id);
                // 檢查是否選擇了包含 "Developers" 的選項
                return (opt && (opt.text.includes("Developers") || opt.id === "A")) || id === "A";
              }) ? "bg-emerald-100 text-emerald-800 px-3 py-1 rounded-full font-medium"
              : checkAnswer(correctAnswers, userAnswers, randomizedOptions, questionIndex, question)
                ? "bg-emerald-100 text-emerald-800 px-3 py-1 rounded-full font-medium"
                : "bg-rose-100 text-rose-800 px-3 py-1 rounded-full font-medium"
            }>
              {
                // 特別處理「At the beginning of the Sprint, who should decide...」問題
                question.question.includes("At the beginning of the Sprint, who should decide") &&
                userAnswers.some(id => {
                  // 從隨機選項中找出對應選項
                  const opt = randomizedOptions.find(o => o.displayId === id);
                  // 檢查是否選擇了包含 "Developers" 的選項 或者選項的原始 ID 是 A
                  return (opt && (opt.text.includes("Developers") || opt.id === "A")) || id === "A";
                }) ? "正確!"
                : checkAnswer(correctAnswers, userAnswers, randomizedOptions, questionIndex, question) ? "正確!" : "錯誤!"
              }
            </span>
            <span className="ml-3 bg-slate-100 text-slate-800 px-3 py-1 rounded-full font-medium">
              正確答案: {correctAnswers.join(', ')}
            </span>
          </div>
        )}
      </CardFooter>
    </Card>
  );
}
