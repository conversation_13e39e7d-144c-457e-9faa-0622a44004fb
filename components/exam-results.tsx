/**
 * Component for displaying exam results including score and incorrect answers
 */
"use client";

import React from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "./ui/card";
import { Button } from "./ui/button";
import { ExamSession, Question } from "../types/question";
import { loadQuestions } from "../lib/exam-service";

interface ExamResultsProps {
  session: ExamSession;
  onRestart: () => void;
  onReviewIncorrect: () => void;
}

/**
 * Displays the exam results with score and options to restart or review incorrect answers
 */
export function ExamResults({ session, onRestart, onReviewIncorrect }: ExamResultsProps) {
  const { score, totalQuestions, incorrectAnswers } = session;
  const correctCount = totalQuestions - incorrectAnswers.length;
  const [allQuestions, setAllQuestions] = React.useState<Question[]>([]);
  
  // Load all questions to get the correct answers for display
  React.useEffect(() => {
    const fetchQuestions = async () => {
      const questions = await loadQuestions();
      setAllQuestions(questions);
    };
    fetchQuestions();
  }, []);
  
  /**
   * Returns appropriate color class based on the score
   */
  const getScoreColorClass = () => {
    if (score >= 80) return "text-emerald-600 font-bold";
    if (score >= 60) return "text-amber-600 font-bold";
    return "text-rose-700 font-bold";
  };

  /**
   * Format selected answers for display
   * 顯示原始選項內容，而不是隨機排列後的選項
   * @param selectedAnswers 用戶選擇的答案 ID（可能是顯示 ID A, B, C, D）
   * @param questionIndex 問題索引
   */
  const formatSelectedAnswers = (selectedAnswers: string[], questionIndex: number) => {
    if (selectedAnswers.length === 0) return "未作答";
    if (allQuestions.length === 0) return selectedAnswers.join(", ");
    
    // 獲取問題對象
    const question = allQuestions[questionIndex];
    if (!question || !question.options) return selectedAnswers.join(", ");
    
    // 嘗試從本地存儲中獲取答案映射關係
    let mappedIds = [...selectedAnswers];
    let mappingFound = false;
    
    try {
      // 當在客戶端環境下執行時
      if (typeof window !== 'undefined') {
        // 獲取答案映射關係
        const mappingsJSON = localStorage.getItem('answer_mapping');
        if (mappingsJSON) {
          const allMappings = JSON.parse(mappingsJSON);
          const mapping = allMappings[questionIndex];
          
          if (mapping) {
            // 將顯示 ID 映射為原始 ID
            mappedIds = selectedAnswers.map(displayId => mapping[displayId] || displayId);
            mappingFound = true;
          }
        }
      }
    } catch (e) {
      console.error('Error retrieving answer mapping:', e);
    }
    
    // 獲取實際選擇的答案內容
    const formattedAnswers = mappedIds.map(id => {
      // 如果 ID 存在於問題選項中，直接使用
      if (question.options[id]) {
        return `${id}. ${question.options[id]}`;
      }
      
      // 如果映射不存在或映射失敗，嘗試尋找所有可能的匹配
      if (!mappingFound) {
        for (const originalId in question.options) {
          // 標記每個原始 ID 對應的選項內容
          const option = `${originalId}. ${question.options[originalId]}`;
          
          // 檢查選項內容是否包含用戶選擇的顯示 ID
          if (option.startsWith(`${id}.`) || option.includes(`${id}. `)) {
            return option;
          }
        }
      }
      
      // 如果無法尋找對應關係，則原樣返回
      return id;
    });
    
    return formattedAnswers.join(", ");
  };

  /**
   * Get the correct answer text for a question
   * 顯示原始正確答案，而不是隨機排列後的答案
   * @param questionIndex 問題索引
   */
  const getCorrectAnswerText = (questionIndex: number) => {
    if (allQuestions.length === 0) return "";
    const question = allQuestions[questionIndex];
    if (!question || !question.options || !question.correctAnswers) return "";
    
    // 將正確答案 ID 轉換為完整的選項描述
    // 使用原始 ID （而不是顯示 ID）確保正確答案的顯示是正確的
    const formattedAnswers = question.correctAnswers.map(id => {
      // 確保 ID 是有效的選項鍵
      if (question.options[id]) {
        return `${id}. ${question.options[id]}`;
      }
      return id; // 如果找不到對應的選項，則原樣返回
    });
    
    return formattedAnswers.join(', ');
  };

  return (
    <div className="w-full max-w-3xl mx-auto">
      <Card className="mb-8">
        <CardHeader>
          <CardTitle className="text-2xl">考試結果</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center mb-6">
            <h3 className="text-4xl font-bold mb-2">
              <span className={getScoreColorClass()}>{score}%</span>
            </h3>
            <p className="text-lg text-slate-800">
              你答對了 {correctCount} 題，共 {totalQuestions} 題
            </p>
            {incorrectAnswers.length > 0 ? (
              <div className="mt-2">
                <span className="inline-block bg-rose-100 text-rose-800 px-3 py-1 rounded-full font-medium text-sm">
                  有 {incorrectAnswers.length} 題回答錯誤
                </span>
              </div>
            ) : (
              <div className="mt-2">
                <span className="inline-block bg-emerald-100 text-emerald-800 px-3 py-1 rounded-full font-medium text-sm">
                  恭喜！你全部答對了！
                </span>
              </div>
            )}
          </div>
          
          <div className="flex flex-col space-y-4 sm:flex-row sm:space-y-0 sm:space-x-4">
            <Button 
              onClick={onRestart} 
              className="flex-1"
              variant="default"
            >
              重新開始考試
            </Button>
            
            {incorrectAnswers.length > 0 && (
              <Button 
                onClick={onReviewIncorrect} 
                className="flex-1"
                variant="outline"
              >
                複習錯誤題目
              </Button>
            )}
          </div>
        </CardContent>
      </Card>
      
      {incorrectAnswers.length > 0 && (
        <div className="mb-6">
          <h3 className="text-xl font-bold mb-4 text-gray-900">錯誤題目摘要</h3>
          <div className="bg-white rounded-lg shadow-md p-4 border border-gray-200">
            <ul className="list-none space-y-4">
              {incorrectAnswers.map((answer, index) => {
                const question = allQuestions[answer.questionIndex];
                return (
                  <li key={index} className="border border-rose-300 rounded-lg p-4 mb-3 bg-white shadow-sm">
                    <div className="text-gray-900 font-semibold text-base">
                      <span className="inline-flex items-center justify-center bg-rose-600 text-white w-6 h-6 rounded-full mr-2 text-sm font-bold">{answer.questionIndex + 1}</span>
                      {question?.question || `問題 ${answer.questionIndex + 1}`}
                    </div>
                    <div className="ml-4 mt-3 text-sm flex items-center">
                      <span className="text-gray-700 font-medium w-20">你選擇了: </span>
                      <span className="font-medium text-rose-800 bg-rose-50 px-2 py-1 rounded border border-rose-200">{formatSelectedAnswers(answer.selectedAnswers, answer.questionIndex)}</span>
                    </div>
                    <div className="ml-4 mt-2 mb-1 text-sm flex items-center">
                      <span className="text-gray-700 font-medium w-20">正確答案: </span>
                      <span className="font-medium text-emerald-800 bg-emerald-50 px-2 py-1 rounded border border-emerald-200">{getCorrectAnswerText(answer.questionIndex)}</span>
                    </div>
                  </li>
                );
              })}
            </ul>
          </div>
        </div>
      )}
    </div>
  );
}
