"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "./ui/button";
import { getLatestExamSession } from "../lib/exam-service";
import { ExamSession } from "../types/question";
import dynamic from 'next/dynamic';

// Dynamically import ExamPage
const ExamPage = dynamic(
  () => import('./exam-page').then(mod => mod.default)
);

/**
 * Client-side only component to avoid hydration errors
 * This component handles the home page functionality
 */
export default function ClientComponent() {
  const [latestSession, setLatestSession] = useState<ExamSession | null>(null);
  const [startExam, setStartExam] = useState(false);

  // Load latest session on client side only
  useEffect(() => {
    try {
      const session = getLatestExamSession();
      setLatestSession(session);
    } catch (error) {
      console.error("Failed to load latest session:", error);
    }
  }, []);

  /**
   * Starts a new exam
   */
  const handleStartExam = () => {
    setStartExam(true);
  };

  if (startExam) {
    return <ExamPage />;
  }

  return (
    <div className="max-w-3xl mx-auto">
      <div className="text-center mb-12">
        <h1 className="text-4xl font-bold mb-4 text-gray-900">通用考試系統</h1>
        <p className="text-xl text-gray-800 font-medium">
          隨機出題、多重選擇、自訂題數的智能考試平台
        </p>
      </div>

      <div className="bg-white rounded-lg shadow-lg p-8 mb-8">
        <h2 className="text-2xl font-semibold mb-6">開始測驗</h2>
        <p className="mb-6 text-gray-800 font-medium">
          這個考試系統具有以下功能：
        </p>
        <ul className="list-disc list-inside mt-2 space-y-2 mb-6 text-gray-800">
          <li className="font-medium">隨機選擇題目，每次考試都不同</li>
          <li className="font-medium">支援單選和多選題型</li>
          <li className="font-medium">自訂考試題數，根據需求調整</li>
          <li className="font-medium">隨機打亂選項順序，增加考試難度</li>
          <li className="font-medium">提供打印功能，方便考試後學習與復習</li>
        </ul>

        <div className="flex flex-col space-y-4 sm:flex-row sm:space-y-0 sm:space-x-4">
          <Button 
            onClick={handleStartExam} 
            className="flex-1 bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-4 rounded-md transition-colors"
          >
            開始新測驗
          </Button>
        </div>
      </div>

      {latestSession && (
        <div className="bg-white rounded-lg shadow-lg p-6 mb-8">
          <h2 className="text-xl font-semibold mb-4">上次測驗結果</h2>
          <div className="flex justify-between items-center">
            <div>
              <p className="text-lg">
                分數: <span className="font-bold">{latestSession.score}%</span>
              </p>
              <p className="text-sm text-gray-600">
                {new Date(latestSession.date).toLocaleDateString()}
              </p>
            </div>
            <Button 
              onClick={handleStartExam}
              className="bg-gray-100 hover:bg-gray-200 text-gray-800 font-medium py-2 px-4 rounded-md transition-colors"
              variant="outline"
            >
              再次測驗
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}
