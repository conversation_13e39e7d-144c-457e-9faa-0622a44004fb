[{"question": "At the daily Scrum meeting, <PERSON> says that he has found a way to solve the problem that he has been trying to solve for a while. He wants to adopt this method immediately to solve the problem. What is the best next step?", "options": {"A": "The Scrum master should agree to let <PERSON> implement this method immediately.", "B": "Let <PERSON> submit this solution to the product owner and report to the developers at the next Scrum meeting.", "C": "After the daily Scrum session, the Scrum master can call for another meeting to discuss it.", "D": "Let all the developers discuss and evaluate <PERSON>'s solutions at the daily Scrum meeting."}, "correctAnswers": ["C"], "hint": "考慮每日站會的目的和時間限制。每日站會應該簡短且專注於三個問題：昨天做了什麼、今天計劃做什麼、有什麼阻礙。詳細的技術討論不應該在每日站會中進行。"}, {"question": "At the beginning of the Sprint, who should decide how the developers should start their work?", "options": {"A": "Developers.", "B": "Project sponsor.", "C": "Scrum master.", "D": "Product owner."}, "correctAnswers": ["A"], "hint": "在 Scrum 中，自組織是一個關鍵原則。思考哪個角色負責決定如何實現產品待辦事項中的功能，以及哪個角色對其工作方式有最大的自主權。"}, {"question": "When the developers have committed to a Sprint goal, what authority does it have?", "options": {"A": "The developers have to assist the product owner with managing the backlog and communicating the vision.", "B": "If the backlog items can not be finished in the current Sprint, the developers are responsible for removing them to the next Sprint.", "C": "The developers are allowed to do whatever is within its authority as long as the work items benefit the Sprint goal.", "D": "The developers have to break down the product backlog defined by the Scrum Master into Sprint backlogs."}, "correctAnswers": ["C"], "hint": "思考 Sprint 目標的目的和開發團隊在 Scrum 中的自主性。Sprint 目標為開發團隊提供了一個明確的方向，但他們如何達成這個目標呢？"}, {"question": "In the Scrum framework, which of the following roles is responsible for making trade-offs based on scopes and schedules?", "options": {"A": "Scrum master.", "B": "Project manager.", "C": "Developers.", "D": "Product owner."}, "correctAnswers": ["D"], "hint": "考慮哪個角色負責管理產品待辦事項、排序及確保其傳達價值。在 Scrum 中，誰有權力決定哪些功能應該優先實施，以及如何在範圍和時間之間做出正確的平衡？"}, {"question": "Which of the following statements is the characteristic of a good Scrum team?", "options": {"A": "A good Scrum team is the sole person responsible for managing the product backlog.", "B": "A good Scrum team recognizes sub-teams.", "C": "A good Scrum team is self-organizing.", "D": "A good Scrum team has similar skills."}, "correctAnswers": ["C"], "hint": "想想 Scrum 團隊的核心特質。一個優秀的 Scrum 團隊能夠自主決定如何完成工作，而不需要外部管理者的詳細指導。團隊成員應該具備不同的技能來完成所有需要的工作。"}, {"question": "What is the MAXIMUM recommended length for the Sprint?", "options": {"A": "45 days.", "B": "90 days.", "C": "60 days.", "D": "30 days."}, "correctAnswers": ["D"], "hint": "Scrum 強調短週期的迭代來快速獲得反饋和學習。想想什麼樣的時間長度能夠保持團隊專注，同時又能頻繁地檢視和調整工作方向？"}, {"question": "What does the Scrum team do during a Sprint?", "options": {"A": "To accomplish the Sprint goal.", "B": "To inspect the increments and adapt the product backlog.", "C": "To clearly express product backlog items.", "D": "To work with the Scrum master to increase the effectiveness of the application of Scrum in the organization."}, "correctAnswers": ["A"], "hint": "每個 Sprint 都有一個明確的目標，這個目標在 Sprint Planning 中設定。團隊在整個 Sprint 期間的工作都應該圍繞著實現這個目標。"}, {"question": "In the Agile manifesto, what is valued over following a plan?", "options": {"A": "Collaborating to customers.", "B": "Responding to changes.", "C": "Communicating information frequently.", "D": "Delivering working software."}, "correctAnswers": ["B"], "hint": "敏捷宣言有四個核心價值觀，其中一個是關於靈活性和適應性的重要性。想想敏捷開發如何處理需求變更和不確定性。"}, {"question": "What does KISS mean in Agile?", "options": {"A": "Simplicity.", "B": "Key identification systems.", "C": "Agile family.", "D": "Lean."}, "correctAnswers": ["A"], "hint": "KISS 是一個常見的設計原則縮寫，強調避免不必要的複雜性。在敏捷開發中，這個原則特別重要，因為它有助於快速交付和維護。"}, {"question": "As a Scrum master, what techniques will you use to help communication between the developers and the product owner?", "options": {"A": "Coach the developers to uses the terminology of business objectives and requirements for discussion.", "B": "Assist the product owner to understand what techniques that will be used during the Sprint.", "C": "All of the statements are true.", "D": "Organize a meeting to facilitate collaboration between them."}, "correctAnswers": ["C"], "hint": "Scrum Master 是一個服務型的領導者，負責促進團隊之間的溝通和協作。考慮 Scrum Master 如何同時幫助開發團隊和產品負責人更好地理解彼此的需求和限制。"}, {"question": "Which of the following statements about the Scrum framework is correct?", "options": {"A": "Scrum is a methodology that defines a complete set of practices for software development.", "B": "Scrum is a framework within which people can address complex adaptive problems.", "C": "Scrum is a process designed specifically for software development projects.", "D": "Scrum is a tool that can be used to implement agile practices."}, "correctAnswers": ["B"], "hint": "理解 Scrum 的本質很重要。它不是一個嚴格的方法論或流程，而是提供了一個結構，讓團隊可以在其中應用自己的實踐來解決複雜問題。"}, {"question": "What is the primary responsibility of a Product Owner in Scrum?", "options": {"A": "Managing the development team's daily activities.", "B": "Maximizing the value of the product and the work of the development team.", "C": "Ensuring the team follows Scrum practices correctly.", "D": "Writing detailed technical specifications for the development team."}, "correctAnswers": ["B"], "hint": "產品負責人的核心職責是確保產品能夠為利益相關者創造最大價值。他們不是項目經理，也不負責管理開發團隊的日常工作。"}, {"question": "What is the purpose of the Daily Scrum meeting?", "options": {"A": "To report progress to the Product Owner.", "B": "To solve technical problems encountered by the team.", "C": "To synchronize activities and create a plan for the next 24 hours.", "D": "To update the Sprint Backlog with new tasks."}, "correctAnswers": ["C"], "hint": "每日站會不是狀態報告會議，也不是問題解決會議。它的主要目的是讓團隊成員之間保持同步，並規劃接下來的工作。"}, {"question": "Which statement about the Sprint Review is correct?", "options": {"A": "It is a formal meeting to assess whether the team followed Scrum practices correctly.", "B": "It is a meeting where only the development team and <PERSON><PERSON> Master participate.", "C": "It is a demonstration of what was accomplished during the Sprint.", "D": "It is a meeting to plan the work for the next Sprint."}, "correctAnswers": ["C"], "hint": "Sprint Review 的重點是展示在這個 Sprint 中完成的工作成果。這是一個與利益相關者分享進展並獲得反饋的機會。"}, {"question": "What happens during the Sprint Retrospective?", "options": {"A": "The team demonstrates the completed work to stakeholders.", "B": "The team inspects how the last Sprint went and identifies improvements for the next Sprint.", "C": "The Product Owner updates the Product Backlog based on stakeholder feedback.", "D": "The team plans the work for the upcoming Sprint."}, "correctAnswers": ["B"], "hint": "Sprint Retrospective 是團隊反思和改進的時間。重點不是產品本身，而是團隊如何協作以及如何改善工作方式。"}, {"question": "In <PERSON><PERSON>, who is responsible for estimating the effort required for Product Backlog items?", "options": {"A": "The Product Owner.", "B": "The Scrum Master.", "C": "The Development Team.", "D": "The Project Manager."}, "correctAnswers": ["C"], "hint": "估算工作量需要技術知識和對實作複雜度的理解。想想誰最了解如何實現產品待辦事項中的功能。"}, {"question": "What is the purpose of a Definition of Done in Scrum?", "options": {"A": "To provide a list of all features that must be completed by the end of the project.", "B": "To create a shared understanding of what it means for work to be complete.", "C": "To document the technical specifications for the product.", "D": "To define the acceptance criteria for each Product Backlog item."}, "correctAnswers": ["B"], "hint": "Definition of Done 確保團隊對於「完成」有共同的理解。它幫助維持品質標準，並確保每個增量都符合可發布的條件。"}, {"question": "Which of the following is NOT a Scrum artifact?", "options": {"A": "Product Backlog.", "B": "Sprint Backlog.", "C": "Burndown Chart.", "D": "Increment."}, "correctAnswers": ["C"], "hint": "Scrum 只定義了三個正式的工件。Burndown Chart 雖然常被使用，但它不是 Scrum 框架中正式定義的工件。"}, {"question": "What is the recommended size of a Development Team in Scrum?", "options": {"A": "3-9 people.", "B": "5-10 people.", "C": "7-12 people.", "D": "There is no recommended size."}, "correctAnswers": ["A"], "hint": "團隊大小需要平衡溝通效率和技能多樣性。太小的團隊可能缺乏必要的技能，太大的團隊則會面臨溝通複雜性的問題。"}, {"question": "Which of the following is a key principle of Agile development?", "options": {"A": "Comprehensive documentation over working software.", "B": "Following a plan over responding to change.", "C": "Customer collaboration over contract negotiation.", "D": "Processes and tools over individuals and interactions."}, "correctAnswers": ["C"], "hint": "這是敏捷宣言四個核心價值觀中的一個。敏捷強調與客戶的持續合作，而不是僅僅依賴合約條款。"}, {"question": "What is a cross-functional team in Scrum?", "options": {"A": "A team where each member specializes in one specific skill.", "B": "A team that has all the skills needed to accomplish their work.", "C": "A team composed of members from different departments.", "D": "A team that works on multiple projects simultaneously."}, "correctAnswers": ["B"], "hint": "跨功能團隊的重點不是每個人都要會所有技能，而是團隊整體具備完成工作所需的所有技能，減少對外部依賴。"}, {"question": "What is the purpose of a Sprint in Scrum?", "options": {"A": "To complete all the items in the Product Backlog.", "B": "To create a potentially releasable product increment.", "C": "To fix all bugs in the existing product.", "D": "To plan the entire project timeline."}, "correctAnswers": ["B"], "hint": "每個 Sprint 的目標是創造一個潛在可發布的產品增量。這意味著 Sprint 結束時，產品應該處於可以發布給用戶的狀態。"}, {"question": "Which statement about the Product Backlog is correct?", "options": {"A": "It is a fixed list of requirements that cannot be changed once the project starts.", "B": "It is created and maintained exclusively by the Development Team.", "C": "It is an ordered list of everything that might be needed in the product.", "D": "It must be completed entirely before the first Sprint can begin."}, "correctAnswers": ["C"], "hint": "產品待辦事項清單是動態的，會根據學習和反饋持續更新。它按優先順序排列，包含所有可能需要的功能和改進。"}, {"question": "What is the main responsibility of a Scrum Master?", "options": {"A": "Assigning tasks to team members.", "B": "Ensuring the team follows the Scrum framework and removing impediments.", "C": "Determining which Product Backlog items should be worked on in each Sprint.", "D": "Writing user stories for the Product Backlog."}, "correctAnswers": ["B"], "hint": "Scrum Master 是一個服務型領導者，不是傳統的項目經理。他們的主要職責是幫助團隊有效地使用 Scrum，並移除阻礙團隊進展的障礙。"}, {"question": "Which of the following is an example of a self-organizing team behavior in Scrum?", "options": {"A": "The Product Owner assigns tasks to team members based on their skills.", "B": "The Scrum Master creates a detailed plan for how the team should implement each feature.", "C": "Team members decide among themselves how to accomplish the work in the Sprint Backlog.", "D": "The project manager approves all technical decisions before they are implemented."}, "correctAnswers": ["C"], "hint": "自組織團隊的核心是團隊成員自主決定如何完成工作。他們不需要外部管理者來分配任務或制定詳細的實施計劃。"}, {"question": "What happens if the Development Team cannot complete all the items they committed to in a Sprint?", "options": {"A": "The Sprint is extended until all items are completed.", "B": "The team must work overtime to complete all items before the Sprint Review.", "C": "Incomplete items are returned to the Product Backlog and re-prioritized.", "D": "The team is penalized for not meeting their commitment."}, "correctAnswers": ["C"], "hint": "Sprint 有固定的時間盒，不應該延長。未完成的工作會回到產品待辦事項清單中，由產品負責人重新排定優先順序。"}, {"question": "What is the primary purpose of a Sprint Planning meeting?", "options": {"A": "To review the work completed in the previous Sprint.", "B": "To select Product Backlog items for the Sprint and decide how to implement them.", "C": "To identify improvements for the team's processes.", "D": "To demonstrate the completed increment to stakeholders."}, "correctAnswers": ["B"], "hint": "Sprint Planning 是為即將到來的 Sprint 做準備的會議。團隊需要決定要做什麼工作，以及如何完成這些工作。"}, {"question": "Which of the following is a characteristic of a well-refined Product Backlog item?", "options": {"A": "It has a detailed technical specification.", "B": "It is assigned to a specific team member.", "C": "It is clear, testable, and small enough to be completed in one Sprint.", "D": "It has a fixed deadline for completion."}, "correctAnswers": ["C"], "hint": "良好的產品待辦事項應該符合 INVEST 原則：獨立、可協商、有價值、可估算、小型、可測試。重點是它應該足夠清晰和小型，能在一個 Sprint 內完成。"}, {"question": "What is the concept of 'empirical process control' in Scrum based on?", "options": {"A": "Detailed upfront planning and risk management.", "B": "Transparency, inspection, and adaptation.", "C": "Command and control management techniques.", "D": "Predictive modeling and statistical analysis."}, "correctAnswers": ["B"], "hint": "經驗過程控制是 Scrum 的基礎，基於這樣的理念：在複雜的環境中，無法預先計劃一切。相反，我們需要透過觀察、檢視和調整來不斷學習和改進。"}, {"question": "Which of the following is an example of a multiple-choice question with multiple correct answers?", "options": {"A": "This is a correct answer.", "B": "This is another correct answer.", "C": "This is an incorrect answer.", "D": "This is also an incorrect answer."}, "correctAnswers": ["A", "B"], "hint": "這是一個範例題目，展示如何處理有多個正確答案的選擇題。在實際考試中，仔細閱讀題目，確認是否需要選擇多個答案。"}]